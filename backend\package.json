{"name": "backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "fix": "node fix-all.js", "test:resend": "ts-node src/tests/resendTest.ts"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@supabase/supabase-js": "^2.39.7", "axios": "^1.9.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "jsonwebtoken": "^9.0.2", "node-cron": "^3.0.3", "nodemailer": "^7.0.2", "pg": "^8.15.6", "resend": "^4.5.1"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22.15.3", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.14", "@types/uuid": "^10.0.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}