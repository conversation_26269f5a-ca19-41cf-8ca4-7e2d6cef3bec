import { Request, Response } from 'express';
import { createClient } from '@supabase/supabase-js';
import config from '../config';
import { TierLevel, getTierById } from '../models/SubscriptionTier';
import { SubscriptionStatus } from '../models/Subscription';
import { updateSubscriptionTier, cancelSubscription } from '../services/subscriptionService';

// Check if Supabase credentials are available
const isSupabaseConfigured = !!(config.supabase.url && config.supabase.serviceKey);
if (!isSupabaseConfigured) {
  console.error('Supabase is not properly configured for admin subscription controller. Please check your environment variables.');
}

// Initialize Supabase client
const supabase = createClient(
  config.supabase.url || '',
  config.supabase.serviceKey || ''
);

/**
 * Get all subscriptions with user details
 */
export async function getAllSubscriptions(req: Request, res: Response) {
  try {
    console.log('Fetching all subscriptions...');

    if (!isSupabaseConfigured) {
      console.error('Cannot fetch subscriptions: Supabase is not configured');
      return res.status(500).json({
        success: false,
        message: 'Database configuration error. Please check server logs.'
      });
    }

    // Role check is now handled by the authorize middleware

    // Get query parameters for pagination
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const offset = (page - 1) * limit;

    // Get filter parameters
    const tierFilter = req.query.tier as TierLevel | undefined;
    const statusFilter = req.query.status as SubscriptionStatus | undefined;
    const searchQuery = req.query.search as string | undefined;
    const startDate = req.query.startDate as string | undefined;
    const endDate = req.query.endDate as string | undefined;

    // Build query
    let query = supabase
      .from('subscriptions')
      .select(`
        *,
        user:users(id, email, first_name, last_name)
      `)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply filters if provided
    if (tierFilter) {
      query = query.eq('tier_id', tierFilter);
    }

    if (statusFilter) {
      query = query.eq('status', statusFilter);
    }

    // Apply date range filters if provided
    if (startDate) {
      query = query.gte('created_at', new Date(startDate).toISOString());
    }

    if (endDate) {
      // Add one day to include the end date fully
      const nextDay = new Date(endDate);
      nextDay.setDate(nextDay.getDate() + 1);
      query = query.lt('created_at', nextDay.toISOString());
    }

    // For search query, we need to handle it differently since we need to search in the joined users table
    let filteredSubscriptions = null;
    if (searchQuery) {
      // First get all subscriptions with user details
      const { data: allSubscriptions, error: searchError } = await supabase
        .from('subscriptions')
        .select(`
          *,
          user:users(id, email, first_name, last_name)
        `);

      if (searchError) {
        console.error('Error fetching subscriptions for search:', searchError);
        return res.status(500).json({
          success: false,
          message: 'Failed to search subscriptions'
        });
      }

      // Filter subscriptions based on search query
      const searchLower = searchQuery.toLowerCase();
      filteredSubscriptions = allSubscriptions.filter(subscription => {
        const user = subscription.user as any;
        return (
          user.email.toLowerCase().includes(searchLower) ||
          user.first_name.toLowerCase().includes(searchLower) ||
          user.last_name.toLowerCase().includes(searchLower) ||
          `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchLower)
        );
      });

      // Apply pagination to filtered results
      filteredSubscriptions = filteredSubscriptions.slice(offset, offset + limit);
    }

    // If we have filtered subscriptions from search, use those instead of executing the query
    let resultSubscriptions;
    let totalCount = 0;

    if (filteredSubscriptions) {
      resultSubscriptions = filteredSubscriptions;

      // For search, we need to count the total filtered results
      const { data: allSubscriptions, error: searchCountError } = await supabase
        .from('subscriptions')
        .select(`
          *,
          user:users(id, email, first_name, last_name)
        `);

      if (searchCountError) {
        console.error('Error counting filtered subscriptions:', searchCountError);
      } else {
        const searchLower = searchQuery!.toLowerCase();
        totalCount = allSubscriptions.filter(subscription => {
          const user = subscription.user as any;
          return (
            user.email.toLowerCase().includes(searchLower) ||
            user.first_name.toLowerCase().includes(searchLower) ||
            user.last_name.toLowerCase().includes(searchLower) ||
            `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchLower)
          );
        }).length;
      }
    } else {
      // Execute query
      const { data, error } = await query;

      if (error) {
        console.error('Error fetching subscriptions:', error);
        return res.status(500).json({
          success: false,
          message: 'Failed to fetch subscriptions'
        });
      }

      resultSubscriptions = data;

      // Get total count for pagination
      let countQuery = supabase.from('subscriptions').select('*', { count: 'exact', head: true });

      // Apply the same filters to the count query
      if (tierFilter) {
        countQuery = countQuery.eq('tier_id', tierFilter);
      }

      if (statusFilter) {
        countQuery = countQuery.eq('status', statusFilter);
      }

      if (startDate) {
        countQuery = countQuery.gte('created_at', new Date(startDate).toISOString());
      }

      if (endDate) {
        const nextDay = new Date(endDate);
        nextDay.setDate(nextDay.getDate() + 1);
        countQuery = countQuery.lt('created_at', nextDay.toISOString());
      }

      const { count, error: countError } = await countQuery;

      if (countError) {
        console.error('Error counting subscriptions:', countError);
      } else {
        totalCount = count || 0;
      }
    }

    return res.status(200).json({
      success: true,
      data: {
        subscriptions: resultSubscriptions,
        pagination: {
          page,
          limit,
          total: totalCount,
          pages: Math.ceil(totalCount / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error in getAllSubscriptions:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

/**
 * Get subscription analytics
 */
export async function getSubscriptionAnalytics(req: Request, res: Response) {
  try {
    console.log('Fetching subscription analytics...');

    if (!isSupabaseConfigured) {
      console.error('Cannot fetch subscription analytics: Supabase is not configured');
      return res.status(500).json({
        success: false,
        message: 'Database configuration error. Please check server logs.'
      });
    }

    // Role check is now handled by the authorize middleware

    // Get date range parameters
    const startDate = req.query.startDate as string | undefined;
    const endDate = req.query.endDate as string | undefined;

    // Default to last 6 months if no date range provided
    let dateRangeStart = new Date();
    dateRangeStart.setMonth(dateRangeStart.getMonth() - 6);

    if (startDate) {
      dateRangeStart = new Date(startDate);
    }

    let dateRangeEnd = new Date();
    if (endDate) {
      dateRangeEnd = new Date(endDate);
      // Add one day to include the end date fully
      dateRangeEnd.setDate(dateRangeEnd.getDate() + 1);
    }

    // Get counts by tier
    // Try to get tier counts using different approaches
    let tierCounts;
    let tierError;

    try {
      // First attempt: Using raw SQL query with execute_sql RPC function
      const tierQuerySQL = `
        SELECT tier_id, COUNT(*) as count
        FROM subscriptions
        WHERE status = '${SubscriptionStatus.ACTIVE}'
        ${startDate ? `AND created_at >= '${dateRangeStart.toISOString()}'` : ''}
        ${endDate ? `AND created_at < '${dateRangeEnd.toISOString()}'` : ''}
        GROUP BY tier_id
      `;

      const result = await supabase.rpc('execute_sql', {
        query: tierQuerySQL
      });

      tierCounts = result.data;
      tierError = result.error;

      // If RPC function fails, fall back to regular query
      if (tierError) {
        console.warn('execute_sql RPC failed, falling back to regular query:', tierError);

        // Fallback: Get all subscriptions and count them in memory
        const { data: allSubscriptions, error: fetchError } = await supabase
          .from('subscriptions')
          .select('tier_id, status, created_at')
          .eq('status', SubscriptionStatus.ACTIVE)
          .gte('created_at', startDate ? dateRangeStart.toISOString() : '1970-01-01')
          .lt('created_at', endDate ? dateRangeEnd.toISOString() : '2099-12-31');

        if (fetchError) {
          tierError = fetchError;
        } else {
          // Count subscriptions by tier manually
          const tierCountMap: Record<string, number> = {};
          allSubscriptions?.forEach(sub => {
            if (!tierCountMap[sub.tier_id]) {
              tierCountMap[sub.tier_id] = 0;
            }
            tierCountMap[sub.tier_id]++;
          });

          // Convert to the expected format
          tierCounts = Object.entries(tierCountMap).map(([tier_id, count]) => ({
            tier_id,
            count
          }));
          tierError = null;
        }
      }
    } catch (error) {
      console.error('Error in tier count query:', error);
      tierError = error;
    }

    if (tierError) {
      console.error('Error counting subscriptions by tier:', tierError);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch subscription analytics'
      });
    }

    // Get counts by status
    // Try to get status counts using different approaches
    let statusCounts;
    let statusError;

    try {
      // First attempt: Using raw SQL query with execute_sql RPC function
      const statusQuerySQL = `
        SELECT status, COUNT(*) as count
        FROM subscriptions
        WHERE 1=1
        ${startDate ? `AND created_at >= '${dateRangeStart.toISOString()}'` : ''}
        ${endDate ? `AND created_at < '${dateRangeEnd.toISOString()}'` : ''}
        GROUP BY status
      `;

      const result = await supabase.rpc('execute_sql', {
        query: statusQuerySQL
      });

      statusCounts = result.data;
      statusError = result.error;

      // If RPC function fails, fall back to regular query
      if (statusError) {
        console.warn('execute_sql RPC failed for status query, falling back to regular query:', statusError);

        // Fallback: Get all subscriptions and count them in memory
        const { data: allSubscriptions, error: fetchError } = await supabase
          .from('subscriptions')
          .select('status, created_at')
          .gte('created_at', startDate ? dateRangeStart.toISOString() : '1970-01-01')
          .lt('created_at', endDate ? dateRangeEnd.toISOString() : '2099-12-31');

        if (fetchError) {
          statusError = fetchError;
        } else {
          // Count subscriptions by status manually
          const statusCountMap: Record<string, number> = {};
          allSubscriptions?.forEach(sub => {
            if (!statusCountMap[sub.status]) {
              statusCountMap[sub.status] = 0;
            }
            statusCountMap[sub.status]++;
          });

          // Convert to the expected format
          statusCounts = Object.entries(statusCountMap).map(([status, count]) => ({
            status,
            count
          }));
          statusError = null;
        }
      }
    } catch (error) {
      console.error('Error in status count query:', error);
      statusError = error;
    }

    if (statusError) {
      console.error('Error counting subscriptions by status:', statusError);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch subscription analytics'
      });
    }

    // Get monthly new subscriptions
    let monthlyQuery = supabase
      .from('subscriptions')
      .select('created_at, tier_id');

    monthlyQuery = monthlyQuery.gte('created_at', dateRangeStart.toISOString());

    if (endDate) {
      monthlyQuery = monthlyQuery.lt('created_at', dateRangeEnd.toISOString());
    }

    const { data: monthlyData, error: monthlyError } = await monthlyQuery;

    if (monthlyError) {
      console.error('Error fetching monthly subscription data:', monthlyError);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch subscription analytics'
      });
    }

    // Process monthly data
    const monthlySubscriptions: Record<string, number> = {};
    const monthlyRevenue: Record<string, number> = {};

    // Get subscription tiers for pricing
    const { SUBSCRIPTION_TIERS } = await import('../models/SubscriptionTier');
    const tierPrices = SUBSCRIPTION_TIERS.reduce((acc, tier) => {
      // Use the India pricing as the default monthly price
      acc[tier.id] = tier.pricing.india;
      return acc;
    }, {} as Record<string, number>);

    // Calculate MRR (Monthly Recurring Revenue)
    let mrr = 0;
    tierCounts?.forEach((item: any) => {
      const tierPrice = tierPrices[item.tier_id] || 0;
      mrr += tierPrice * item.count;
    });

    // Calculate ARR (Annual Recurring Revenue)
    const arr = mrr * 12;

    monthlyData?.forEach(subscription => {
      const date = new Date(subscription.created_at);
      const monthYear = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      // Count subscriptions by month
      if (!monthlySubscriptions[monthYear]) {
        monthlySubscriptions[monthYear] = 0;
      }
      monthlySubscriptions[monthYear]++;

      // Calculate revenue by month
      if (!monthlyRevenue[monthYear]) {
        monthlyRevenue[monthYear] = 0;
      }
      const tierPrice = tierPrices[subscription.tier_id] || 0;
      monthlyRevenue[monthYear] += tierPrice;
    });

    return res.status(200).json({
      success: true,
      data: {
        tierCounts,
        statusCounts,
        monthlySubscriptions,
        revenue: {
          monthly: monthlyRevenue,
          mrr,
          arr
        }
      }
    });
  } catch (error) {
    console.error('Error in getSubscriptionAnalytics:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

/**
 * Update a user's subscription (admin only)
 */
export async function updateUserSubscription(req: Request, res: Response) {
  try {
    // Role check is now handled by the authorize middleware

    const { subscriptionId } = req.params;
    const { tierId } = req.body;

    if (!subscriptionId || !tierId) {
      return res.status(400).json({
        success: false,
        message: 'Subscription ID and tier ID are required'
      });
    }

    // Update the subscription
    const updatedSubscription = await updateSubscriptionTier(subscriptionId, tierId);

    if (!updatedSubscription) {
      return res.status(500).json({
        success: false,
        message: 'Failed to update subscription'
      });
    }

    return res.status(200).json({
      success: true,
      data: updatedSubscription,
      message: 'Subscription updated successfully'
    });
  } catch (error) {
    console.error('Error in updateUserSubscription:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

/**
 * Cancel a user's subscription (admin only)
 */
export async function cancelUserSubscription(req: Request, res: Response) {
  try {
    // Role check is now handled by the authorize middleware

    const { subscriptionId } = req.params;
    const { cancelImmediately } = req.body;

    if (!subscriptionId) {
      return res.status(400).json({
        success: false,
        message: 'Subscription ID is required'
      });
    }

    // Cancel the subscription
    const canceledSubscription = await cancelSubscription(subscriptionId, cancelImmediately);

    if (!canceledSubscription) {
      return res.status(500).json({
        success: false,
        message: 'Failed to cancel subscription'
      });
    }

    return res.status(200).json({
      success: true,
      data: canceledSubscription,
      message: 'Subscription canceled successfully'
    });
  } catch (error) {
    console.error('Error in cancelUserSubscription:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}
