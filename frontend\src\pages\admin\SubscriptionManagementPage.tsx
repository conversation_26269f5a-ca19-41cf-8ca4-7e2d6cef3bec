import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../../utils/api';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { Dialog } from '../../components/ui/Dialog';
import { TierLevel, SubscriptionStatus } from '../../types/subscription';
import { formatDate } from '../../utils/dateUtils';
import { toast } from 'react-hot-toast';
import { FiDownload, FiSearch, FiFilter, FiCalendar } from 'react-icons/fi';
import Layout from '../../components/Layout';
import { useAuth } from '../../contexts/AuthContext';

interface Subscription {
  id: string;
  user_id: string;
  tier_id: TierLevel;
  status: SubscriptionStatus;
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at: string;
  user: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
  };
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

const SubscriptionManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);


  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filters
  const [tierFilter, setTierFilter] = useState<TierLevel | ''>('');
  const [statusFilter, setStatusFilter] = useState<SubscriptionStatus | ''>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [dateRange, setDateRange] = useState<{start?: string; end?: string}>({});

  // Edit modal
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedSubscription, setSelectedSubscription] = useState<Subscription | null>(null);
  const [newTierId, setNewTierId] = useState<TierLevel | ''>('');

  // Cancel modal
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [cancelImmediately, setCancelImmediately] = useState(false);

  // Fetch subscriptions
  const fetchSubscriptions = async (page = 1) => {
    try {
      setIsLoading(true);
      setError(null);

      let url = `/admin/subscription/subscriptions?page=${page}&limit=${pagination.limit}`;

      if (tierFilter) {
        url += `&tier=${tierFilter}`;
      }

      if (statusFilter) {
        url += `&status=${statusFilter}`;
      }

      if (searchQuery) {
        url += `&search=${encodeURIComponent(searchQuery)}`;
      }

      if (dateRange.start) {
        url += `&startDate=${dateRange.start}`;
      }

      if (dateRange.end) {
        url += `&endDate=${dateRange.end}`;
      }

      const response = await api.get(url);

      if (response.data.success) {
        setSubscriptions(response.data.data.subscriptions);
        setPagination(response.data.data.pagination);
      } else {
        setError(response.data.message || 'Failed to fetch subscriptions');
      }
    } catch (err: any) {
      console.error('Error fetching subscriptions:', err);
      setError(err.response?.data?.message || err.message || 'An error occurred while fetching subscriptions');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchSubscriptions();
  }, [tierFilter, statusFilter, searchQuery, dateRange.start, dateRange.end]);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    fetchSubscriptions(newPage);
  };

  // Handle edit subscription
  const handleEditSubscription = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    setNewTierId(subscription.tier_id);
    setIsEditModalOpen(true);
  };

  // Handle save subscription changes
  const handleSaveChanges = async () => {
    if (!selectedSubscription || !newTierId) return;

    try {
      setIsLoading(true);

      const response = await api.put(`/admin/subscription/subscriptions/${selectedSubscription.id}`, {
        tierId: newTierId
      });

      if (response.data.success) {
        toast.success('Subscription updated successfully');
        setIsEditModalOpen(false);
        fetchSubscriptions(pagination.page);
      } else {
        toast.error(response.data.message || 'Failed to update subscription');
      }
    } catch (err: any) {
      console.error('Error updating subscription:', err);
      toast.error(err.message || 'An error occurred while updating subscription');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle cancel subscription
  const handleCancelSubscription = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    setCancelImmediately(false);
    setIsCancelModalOpen(true);
  };

  // Handle confirm cancel
  const handleConfirmCancel = async () => {
    if (!selectedSubscription) return;

    try {
      setIsLoading(true);

      const response = await api.post(`/admin/subscription/subscriptions/${selectedSubscription.id}/cancel`, {
        cancelImmediately
      });

      if (response.data.success) {
        toast.success('Subscription canceled successfully');
        setIsCancelModalOpen(false);
        fetchSubscriptions(pagination.page);
      } else {
        toast.error(response.data.message || 'Failed to cancel subscription');
      }
    } catch (err: any) {
      console.error('Error canceling subscription:', err);
      toast.error(err.message || 'An error occurred while canceling subscription');
    } finally {
      setIsLoading(false);
    }
  };

  // Export subscriptions to CSV
  const exportToCSV = () => {
    if (subscriptions.length === 0) {
      toast.error('No data to export');
      return;
    }

    // Create CSV header
    const headers = [
      'User ID',
      'User Email',
      'User Name',
      'Tier',
      'Status',
      'Current Period Start',
      'Current Period End',
      'Cancel At Period End',
      'Created At'
    ];

    // Create CSV rows
    const rows = subscriptions.map(subscription => [
      subscription.user_id,
      subscription.user.email,
      `${subscription.user.first_name} ${subscription.user.last_name}`,
      subscription.tier_id === TierLevel.FREE ? 'Free' :
        subscription.tier_id === TierLevel.PRO ? 'Pro' : 'Pro+',
      subscription.status,
      formatDate(subscription.current_period_start),
      formatDate(subscription.current_period_end),
      subscription.cancel_at_period_end ? 'Yes' : 'No',
      formatDate(subscription.created_at)
    ]);

    // Combine header and rows
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.join(','))
    ].join('\n');

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `subscriptions_export_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Export successful');
  };

  return (
    <Layout>
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Subscription Management</h1>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={exportToCSV}
            disabled={subscriptions.length === 0}
          >
            <FiDownload className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
          <Button onClick={() => navigate('/admin/subscription/analytics')}>
            View Analytics
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-6 p-4">
        <div className="mb-4">
          <label className="mb-1 block text-sm font-medium text-gray-700">
            Search
          </label>
          <div className="flex">
            <input
              type="text"
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              placeholder="Search by user name or email"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            {searchQuery && (
              <button
                className="ml-2 rounded-md border border-gray-300 px-3 py-2 text-sm"
                onClick={() => setSearchQuery('')}
              >
                Clear
              </button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Tier
            </label>
            <select
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              value={tierFilter}
              onChange={(e) => setTierFilter(e.target.value as TierLevel | '')}
            >
              <option value="">All Tiers</option>
              <option value={TierLevel.FREE}>Free</option>
              <option value={TierLevel.PRO}>Pro</option>
              <option value={TierLevel.PRO_PLUS}>Pro+</option>
            </select>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Status
            </label>
            <select
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as SubscriptionStatus | '')}
            >
              <option value="">All Statuses</option>
              <option value={SubscriptionStatus.ACTIVE}>Active</option>
              <option value={SubscriptionStatus.CANCELED}>Canceled</option>
              <option value={SubscriptionStatus.TRIALING}>Trialing</option>
              <option value={SubscriptionStatus.PAST_DUE}>Past Due</option>
            </select>
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Start Date
            </label>
            <input
              type="date"
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              value={dateRange.start || ''}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
            />
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              End Date
            </label>
            <input
              type="date"
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              value={dateRange.end || ''}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
            />
          </div>
        </div>

        <div className="mt-4 flex justify-end">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              setTierFilter('');
              setStatusFilter('');
              setSearchQuery('');
              setDateRange({});
            }}
          >
            Reset Filters
          </Button>
        </div>
      </Card>

      {/* Subscriptions Table */}
      <Card className="overflow-hidden">
        {isLoading && (
          <div className="flex h-24 items-center justify-center">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"></div>
          </div>
        )}

        {error && (
          <div className="p-4 text-center text-red-500">
            <p>{error}</p>
          </div>
        )}

        {!isLoading && !error && subscriptions.length === 0 && (
          <div className="p-4 text-center text-gray-500">
            <p>No subscriptions found</p>
          </div>
        )}

        {!isLoading && !error && subscriptions.length > 0 && (
          <div className="overflow-x-auto">
            <table className="w-full table-auto">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    User
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Tier
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Status
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Current Period
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Created
                  </th>
                  <th className="px-4 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {subscriptions.map((subscription) => (
                  <tr key={subscription.id}>
                    <td className="whitespace-nowrap px-4 py-2">
                      <div>
                        <div className="font-medium text-gray-900">
                          {subscription.user.first_name} {subscription.user.last_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {subscription.user.email}
                        </div>
                      </div>
                    </td>
                    <td className="whitespace-nowrap px-4 py-2">
                      <span className={`rounded-full px-2 py-1 text-xs font-medium ${
                        subscription.tier_id === TierLevel.FREE
                          ? 'bg-gray-100 text-gray-800'
                          : subscription.tier_id === TierLevel.PRO
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-purple-100 text-purple-800'
                      }`}>
                        {subscription.tier_id === TierLevel.FREE
                          ? 'Free'
                          : subscription.tier_id === TierLevel.PRO
                          ? 'Pro'
                          : 'Pro+'}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-4 py-2">
                      <span className={`rounded-full px-2 py-1 text-xs font-medium ${
                        subscription.status === SubscriptionStatus.ACTIVE
                          ? 'bg-green-100 text-green-800'
                          : subscription.status === SubscriptionStatus.TRIALING
                          ? 'bg-blue-100 text-blue-800'
                          : subscription.status === SubscriptionStatus.CANCELED
                          ? 'bg-red-100 text-red-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {subscription.status}
                      </span>
                    </td>
                    <td className="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                      {formatDate(subscription.current_period_start)} to {formatDate(subscription.current_period_end)}
                    </td>
                    <td className="whitespace-nowrap px-4 py-2 text-sm text-gray-500">
                      {formatDate(subscription.created_at)}
                    </td>
                    <td className="whitespace-nowrap px-4 py-2">
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEditSubscription(subscription)}
                          disabled={subscription.status === SubscriptionStatus.CANCELED}
                        >
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleCancelSubscription(subscription)}
                          disabled={subscription.status === SubscriptionStatus.CANCELED}
                        >
                          Cancel
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </Card>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="mt-4 flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-700">
              Showing <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span> to{' '}
              <span className="font-medium">
                {Math.min(pagination.page * pagination.limit, pagination.total)}
              </span>{' '}
              of <span className="font-medium">{pagination.total}</span> results
            </p>
          </div>
          <div className="flex space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
            >
              Previous
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.pages}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Edit Subscription Modal */}
      <Dialog
        open={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Subscription"
      >
        <div className="mt-2">
          <p className="text-sm text-gray-500">
            Update subscription tier for {selectedSubscription?.user.first_name} {selectedSubscription?.user.last_name}
          </p>

          <div className="mt-4">
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Tier
            </label>
            <select
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              value={newTierId}
              onChange={(e) => setNewTierId(e.target.value as TierLevel)}
            >
              <option value={TierLevel.FREE}>Free</option>
              <option value={TierLevel.PRO}>Pro</option>
              <option value={TierLevel.PRO_PLUS}>Pro+</option>
            </select>
          </div>
        </div>

        <div className="mt-4 flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={() => setIsEditModalOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSaveChanges}
            disabled={isLoading || !newTierId}
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </Dialog>

      {/* Cancel Subscription Modal */}
      <Dialog
        open={isCancelModalOpen}
        onClose={() => setIsCancelModalOpen(false)}
        title="Cancel Subscription"
      >
        <div className="mt-2">
          <p className="text-sm text-gray-500">
            Are you sure you want to cancel the subscription for {selectedSubscription?.user.first_name} {selectedSubscription?.user.last_name}?
          </p>

          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                checked={cancelImmediately}
                onChange={(e) => setCancelImmediately(e.target.checked)}
              />
              <span className="ml-2 text-sm text-gray-700">
                Cancel immediately (otherwise will cancel at end of billing period)
              </span>
            </label>
          </div>
        </div>

        <div className="mt-4 flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={() => setIsCancelModalOpen(false)}
            disabled={isLoading}
          >
            No, Keep Subscription
          </Button>
          <Button
            variant="danger"
            onClick={handleConfirmCancel}
            disabled={isLoading}
          >
            {isLoading ? 'Canceling...' : 'Yes, Cancel Subscription'}
          </Button>
        </div>
      </Dialog>
    </Layout>
  );
};

export default SubscriptionManagementPage;
