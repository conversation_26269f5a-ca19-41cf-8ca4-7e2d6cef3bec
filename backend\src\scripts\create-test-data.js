// This script creates test tenants, users, and subscriptions for development
// Run with: node create-test-data.js

const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Test data configuration
const TEST_PASSWORD = 'TestAdmin123!';
const TIERS = ['free', 'pro', 'pro_plus'];

const testTenants = [
  { name: 'Acme Dental Clinic', email: '<EMAIL>', phone: '******-0101', address: '123 Main St, New York, NY' },
  { name: 'Bella Beauty Salon', email: '<EMAIL>', phone: '******-0102', address: '456 Oak Ave, Los Angeles, CA' },
  { name: 'City Fitness Center', email: '<EMAIL>', phone: '******-0103', address: '789 Pine Rd, Chicago, IL' },
  { name: 'Downtown Law Office', email: '<EMAIL>', phone: '******-0104', address: '321 Elm St, Houston, TX' },
  { name: 'Elite Consulting Group', email: '<EMAIL>', phone: '******-0105', address: '654 Maple Dr, Phoenix, AZ' }
];

async function clearExistingTestData() {
  console.log('🧹 Clearing existing test data...');
  
  try {
    // Delete existing subscriptions for test users
    await supabase.from('subscriptions').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    
    // Delete existing test users (except super admin)
    await supabase.from('users').delete().neq('role', 'super_admin');
    
    // Delete existing test tenants
    await supabase.from('tenants').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    
    console.log('✅ Existing test data cleared');
  } catch (error) {
    console.error('Error clearing test data:', error);
  }
}

async function createTestTenants() {
  console.log('🏢 Creating test tenants...');
  
  const createdTenants = [];
  
  for (let i = 0; i < testTenants.length; i++) {
    const tenant = testTenants[i];
    const tenantId = uuidv4();
    
    try {
      // Create tenant
      const { data: tenantData, error: tenantError } = await supabase
        .from('tenants')
        .insert({
          id: tenantId,
          name: tenant.name,
          email: tenant.email,
          phone: tenant.phone,
          address: tenant.address,
          active: true
        })
        .select()
        .single();
      
      if (tenantError) {
        console.error(`Error creating tenant ${tenant.name}:`, tenantError);
        continue;
      }
      
      // Create admin user for tenant
      const userId = uuidv4();
      const hashedPassword = await bcrypt.hash(TEST_PASSWORD, 10);
      
      const { data: userData, error: userError } = await supabase
        .from('users')
        .insert({
          id: userId,
          tenant_id: tenantId,
          first_name: `Admin${i + 1}`,
          last_name: 'User',
          email: tenant.email,
          password: hashedPassword,
          phone: tenant.phone,
          role: 'admin',
          active: true
        })
        .select()
        .single();
      
      if (userError) {
        console.error(`Error creating user for ${tenant.name}:`, userError);
        continue;
      }
      
      // Create subscription for user
      const tier = TIERS[i % TIERS.length];
      const now = new Date();
      const endDate = new Date();
      endDate.setDate(now.getDate() + 30);
      
      const { error: subscriptionError } = await supabase
        .from('subscriptions')
        .insert({
          id: uuidv4(),
          user_id: userId,
          tier_id: tier,
          status: 'active',
          current_period_start: now.toISOString(),
          current_period_end: endDate.toISOString(),
          cancel_at_period_end: false,
          created_at: now.toISOString(),
          updated_at: now.toISOString()
        });
      
      if (subscriptionError) {
        console.error(`Error creating subscription for ${tenant.name}:`, subscriptionError);
        continue;
      }
      
      createdTenants.push({
        tenant: tenantData,
        user: userData,
        tier: tier,
        email: tenant.email,
        password: TEST_PASSWORD
      });
      
      console.log(`✅ Created: ${tenant.name} (${tier} tier)`);
      
    } catch (error) {
      console.error(`Error processing tenant ${tenant.name}:`, error);
    }
  }
  
  return createdTenants;
}

async function createAdditionalSubscriptions() {
  console.log('📊 Creating additional subscription variations...');
  
  // Create some additional users with different subscription statuses
  const additionalUsers = [
    { name: 'Trial User', tier: 'pro', status: 'trialing' },
    { name: 'Canceled User', tier: 'free', status: 'canceled' },
    { name: 'Past Due User', tier: 'pro_plus', status: 'past_due' }
  ];
  
  for (let i = 0; i < additionalUsers.length; i++) {
    const userConfig = additionalUsers[i];
    const userId = uuidv4();
    const email = `${userConfig.name.toLowerCase().replace(' ', '')}@example.com`;
    
    try {
      const hashedPassword = await bcrypt.hash(TEST_PASSWORD, 10);
      
      // Create user without tenant (client user)
      const { data: userData, error: userError } = await supabase
        .from('users')
        .insert({
          id: userId,
          tenant_id: null,
          first_name: userConfig.name.split(' ')[0],
          last_name: userConfig.name.split(' ')[1],
          email: email,
          password: hashedPassword,
          role: 'client',
          active: true
        })
        .select()
        .single();
      
      if (userError) {
        console.error(`Error creating ${userConfig.name}:`, userError);
        continue;
      }
      
      // Create subscription
      const now = new Date();
      const endDate = new Date();
      endDate.setDate(now.getDate() + (userConfig.status === 'past_due' ? -5 : 30));
      
      const { error: subscriptionError } = await supabase
        .from('subscriptions')
        .insert({
          id: uuidv4(),
          user_id: userId,
          tier_id: userConfig.tier,
          status: userConfig.status,
          current_period_start: now.toISOString(),
          current_period_end: endDate.toISOString(),
          cancel_at_period_end: userConfig.status === 'canceled',
          created_at: now.toISOString(),
          updated_at: now.toISOString()
        });
      
      if (subscriptionError) {
        console.error(`Error creating subscription for ${userConfig.name}:`, subscriptionError);
        continue;
      }
      
      console.log(`✅ Created: ${userConfig.name} (${userConfig.tier} - ${userConfig.status})`);
      
    } catch (error) {
      console.error(`Error processing ${userConfig.name}:`, error);
    }
  }
}

async function main() {
  console.log('🚀 Starting test data creation...\n');
  
  try {
    // Clear existing test data
    await clearExistingTestData();
    
    // Create test tenants with admin users and subscriptions
    const tenants = await createTestTenants();
    
    // Create additional subscription variations
    await createAdditionalSubscriptions();
    
    console.log('\n🎉 Test data creation completed!\n');
    
    console.log('📋 TENANT ADMIN LOGIN CREDENTIALS:');
    console.log('=====================================');
    tenants.forEach((tenant, index) => {
      console.log(`${index + 1}. ${tenant.tenant.name}`);
      console.log(`   Email: ${tenant.email}`);
      console.log(`   Password: ${tenant.password}`);
      console.log(`   Tier: ${tenant.tier}`);
      console.log('');
    });
    
    console.log('🔑 SUPER ADMIN CREDENTIALS:');
    console.log('===========================');
    console.log('Email: <EMAIL>');
    console.log('Password: Admin123!');
    console.log('');
    
    console.log('💡 ADDITIONAL TEST USERS:');
    console.log('=========================');
    console.log('1. <EMAIL> (Password: TestAdmin123!)');
    console.log('2. <EMAIL> (Password: TestAdmin123!)');
    console.log('3. <EMAIL> (Password: TestAdmin123!)');
    console.log('');
    
    console.log('✨ You can now test the subscription management features!');
    
  } catch (error) {
    console.error('❌ Error creating test data:', error);
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);
