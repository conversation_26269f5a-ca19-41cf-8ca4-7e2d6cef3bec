# 🔑 Scheduly Test Credentials

## 🎯 Super Admin (Subscription Management Access)

**For accessing subscription management and analytics:**

- **Email**: `<EMAIL>`
- **Password**: `Admin123!`
- **Access**: Full subscription management, analytics, super admin dashboard
- **URLs**: 
  - Subscription Management: `http://localhost:5173/admin/subscription`
  - Subscription Analytics: `http://localhost:5173/admin/subscription/analytics`
  - Super Admin Dashboard: `http://localhost:5173/super-admin/dashboard`

---

## 🏢 Tenant Admin Accounts

**For testing regular tenant features:**

### 1. Acme Dental Clinic (Free Tier)
- **Email**: `<EMAIL>`
- **Password**: `TestAdmin123!`
- **Tier**: Free
- **Business**: Acme Dental Clinic

### 2. Bella Beauty Salon (Pro Tier)
- **Email**: `<EMAIL>`
- **Password**: `TestAdmin123!`
- **Tier**: Pro
- **Business**: Bella Beauty Salon

### 3. City Fitness Center (Pro Plus Tier)
- **Email**: `<EMAIL>`
- **Password**: `TestAdmin123!`
- **Tier**: Pro Plus
- **Business**: City Fitness Center

### 4. Downtown Law Office (Free Tier)
- **Email**: `<EMAIL>`
- **Password**: `TestAdmin123!`
- **Tier**: Free
- **Business**: Downtown Law Office

### 5. Elite Consulting Group (Pro Tier)
- **Email**: `<EMAIL>`
- **Password**: `TestAdmin123!`
- **Tier**: Pro
- **Business**: Elite Consulting Group

---

## 👥 Additional Test Users

**For testing different subscription statuses:**

### Trial User (Pro - Trialing)
- **Email**: `<EMAIL>`
- **Password**: `TestAdmin123!`
- **Status**: Trialing
- **Tier**: Pro

### Canceled User (Free - Canceled)
- **Email**: `<EMAIL>`
- **Password**: `TestAdmin123!`
- **Status**: Canceled
- **Tier**: Free

### Past Due User (Pro Plus - Past Due)
- **Email**: `<EMAIL>`
- **Password**: `TestAdmin123!`
- **Status**: Past Due
- **Tier**: Pro Plus

---

## 🚀 Quick Testing Guide

### Test Subscription Management:
1. Login as Super Admin (`<EMAIL>`)
2. Navigate to: `http://localhost:5173/admin/subscription`
3. You should see all 8 subscriptions with different tiers and statuses

### Test Subscription Analytics:
1. Login as Super Admin (`<EMAIL>`)
2. Navigate to: `http://localhost:5173/admin/subscription/analytics`
3. You should see charts and revenue data

### Test Tenant Features:
1. Login as any tenant admin (e.g., `<EMAIL>`)
2. Navigate to: `http://localhost:5173/dashboard`
3. Test appointment booking, services, staff management, etc.

---

## 📊 Expected Subscription Data

**Tier Distribution:**
- Free: 3 subscriptions
- Pro: 3 subscriptions  
- Pro Plus: 2 subscriptions

**Status Distribution:**
- Active: 5 subscriptions
- Trialing: 1 subscription
- Canceled: 1 subscription
- Past Due: 1 subscription

---

## 🔄 Regenerating Test Data

If you need to recreate the test data:

```bash
cd backend
node src/scripts/create-test-data.js
```

This will clear all existing test data and create fresh tenants, users, and subscriptions.

---

## 🎉 Features to Test

### Subscription Management:
- ✅ View all subscriptions
- ✅ Filter by tier and status
- ✅ Search by user name/email
- ✅ Edit subscription tiers
- ✅ Cancel subscriptions
- ✅ Export to CSV

### Subscription Analytics:
- ✅ Tier distribution charts
- ✅ Revenue analytics (MRR/ARR)
- ✅ Monthly subscription trends
- ✅ Status breakdown

### Tenant Features:
- ✅ Dashboard
- ✅ Appointment management
- ✅ Service management
- ✅ Staff management
- ✅ Client management
- ✅ Analytics (tier-dependent)

---

**Last Updated**: June 14, 2025
**Status**: ✅ All credentials tested and working
