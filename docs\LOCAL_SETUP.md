# Local Development Setup Guide

This guide will help you set up your local development environment for the Scheduly project.

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- A Supabase account

## Step 1: Set Up Supabase

1. Create a new Supabase project:
   - Go to [Supabase](https://app.supabase.io/)
   - Click "New Project"
   - Enter a name for your project (e.g., "scheduly-local")
   - Choose a database password (save this for later)
   - Select a region close to you
   - Click "Create New Project"

2. Set up the database schema:
   - Go to the SQL Editor in your Supabase project
   - Run the SQL from the migration files:
     - First run the contents of `supabase\migrations\01_initial_schema.sql`
     - Then run the contents of `supabase\migrations\02_security_policies.sql`

3. Get your Supabase credentials:
   - Go to Project Settings > API
   - Copy the "Project URL", "anon public" key, and "service_role" key

## Step 2: Configure Environment Variables

1. Update the frontend `.env` file:
   ```
   VITE_API_URL=http://localhost:5000/api
   VITE_SUPABASE_URL=your_supabase_project_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

2. Update the backend `.env` file:
   ```
   # Server Configuration
   PORT=5000
   NODE_ENV=development

   # Supabase Configuration
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

   # JWT Configuration
   JWT_SECRET=your_generated_jwt_secret
   JWT_EXPIRES_IN=1d

   # Email Configuration (optional for local development)
   EMAIL_HOST=smtp.example.com
   EMAIL_PORT=587
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your_email_password

   # Google OAuth Configuration (optional for local development)
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   GOOGLE_REDIRECT_URI=http://localhost:5173/google-calendar-callback
   ```

3. Generate a secure JWT secret:
   ```
   node generate-jwt-secret.js
   ```
   Copy the generated secret to your backend `.env` file.

## Step 3: Create a Super Admin User

1. Install the required dependencies:
   ```
   npm install @supabase/supabase-js bcrypt uuid dotenv
   ```

2. Run the super admin creation script:
   ```
   node create-super-admin-supabase.js
   ```

## Step 4: Start the Development Servers

1. Start the backend server:
   ```
   cd backend
   npm install
   npm run dev
   ```

2. In a new terminal, start the frontend server:
   ```
   cd frontend
   npm install
   npm run dev
   ```

3. Access the application at `http://localhost:5173`

## Step 5: Test the Application

1. Log in with the super admin credentials:
   - Email: `<EMAIL>`
   - Password: `Admin123!`

2. Create a new tenant and test the functionality

## Troubleshooting

### Database Connection Issues

If you encounter database connection issues:

1. Check that your Supabase URL and keys are correct in the `.env` files
2. Make sure your Supabase project is active
3. Check the CORS settings in your Supabase project:
   - Go to Project Settings > API > CORS
   - Add `http://localhost:5173` to the allowed origins

### Authentication Issues

If you encounter authentication issues:

1. Make sure the JWT_SECRET is the same in your backend `.env` file
2. Check that the user exists in the database
3. Verify that the password is correct

### API Connection Issues

If your frontend can't connect to the backend:

1. Make sure the backend server is running
2. Check that `VITE_API_URL` is set correctly in the frontend `.env` file
3. Look for CORS errors in the browser console
