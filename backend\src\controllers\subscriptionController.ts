import { Request, Response } from 'express';
import {
  getUserSubscription,
  createSubscription,
  updateSubscriptionTier,
  cancelSubscription,
  getAllTiers
} from '../services/subscriptionService';
import { TierLevel, getTierById } from '../models/SubscriptionTier';
import { isSubscriptionActive } from '../models/Subscription';
import config from '../config';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

// Get current user's subscription
export async function getCurrentSubscription(req: Request, res: Response) {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const subscription = await getUserSubscription(userId);

    return res.status(200).json({
      success: true,
      data: {
        subscription,
        isActive: isSubscriptionActive(subscription)
      }
    });
  } catch (error) {
    console.error('Error in getCurrentSubscription:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

// Get all available subscription tiers
export async function getSubscriptionTiers(req: Request, res: Response) {
  try {
    const tiers = await getAllTiers();

    return res.status(200).json({
      success: true,
      data: tiers
    });
  } catch (error) {
    console.error('Error in getSubscriptionTiers:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}

// Subscribe to a new tier
export async function subscribe(req: Request, res: Response) {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const { tierId, paymentMethodId } = req.body;

    if (!tierId || !Object.values(TierLevel).includes(tierId)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid tier ID'
      });
    }

    // Check if user already has an active subscription
    const existingSubscription = await getUserSubscription(userId);

    if (existingSubscription && isSubscriptionActive(existingSubscription)) {
      // Update existing subscription
      const updated = await updateSubscriptionTier(
        existingSubscription.id,
        tierId
      );

      if (!updated) {
        return res.status(500).json({
          success: false,
          message: 'Failed to update subscription'
        });
      }

      return res.status(200).json({
        success: true,
        data: updated,
        message: 'Subscription updated successfully'
      });
    }

    // Create new subscription
    const subscription = await createSubscription(
      userId,
      tierId,
      paymentMethodId
    );

    if (!subscription) {
      return res.status(500).json({
        success: false,
        message: 'Failed to create subscription'
      });
    }

    return res.status(201).json({
      success: true,
      data: subscription,
      message: 'Subscription created successfully'
    });
  } catch (error) {
    console.error('Error in subscribe:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}









// Cancel subscription
export async function cancelUserSubscription(req: Request, res: Response) {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const { cancelImmediately } = req.body;

    // Get current subscription
    const subscription = await getUserSubscription(userId);

    if (!subscription || !isSubscriptionActive(subscription)) {
      return res.status(400).json({
        success: false,
        message: 'No active subscription found'
      });
    }

    // Cancel subscription
    const canceled = await cancelSubscription(
      subscription.id,
      cancelImmediately
    );

    if (!canceled) {
      return res.status(500).json({
        success: false,
        message: 'Failed to cancel subscription'
      });
    }

    return res.status(200).json({
      success: true,
      data: canceled,
      message: cancelImmediately
        ? 'Subscription canceled immediately'
        : 'Subscription will be canceled at the end of the billing period'
    });
  } catch (error) {
    console.error('Error in cancelUserSubscription:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error'
    });
  }
}
