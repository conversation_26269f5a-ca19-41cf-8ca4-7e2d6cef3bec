{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:type-check": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"@headlessui/react": "^1.7.18", "@heroicons/react": "^2.1.1", "@supabase/supabase-js": "^2.39.7", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-router-dom": "^7.5.3", "tailwind-merge": "^2.2.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@tailwindcss/postcss": "^4.1.5", "@types/react": "^19.0.10", "@types/react-datepicker": "^6.2.0", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.1", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}