import dotenv from 'dotenv';

dotenv.config();

// Email Configuration (Legacy Nodemailer)
export const emailConfig = {
  host: process.env.EMAIL_HOST || 'smtp.example.com',
  port: parseInt(process.env.EMAIL_PORT || '587'),
  secure: process.env.EMAIL_PORT === '465', // true for 465, false for other ports
  auth: {
    user: process.env.EMAIL_USER || '<EMAIL>',
    pass: process.env.EMAIL_PASS || 'your_email_password',
  },
  from: process.env.EMAIL_FROM || 'Scheduly <<EMAIL>>',
};

// Resend Email Configuration
export const resendConfig = {
  apiKey: process.env.RESEND_API_KEY || '',
  from: process.env.RESEND_FROM || 'Scheduly <<EMAIL>>',
  region: process.env.RESEND_REGION || 'us', // 'us', 'eu', or 'ap' (Asia Pacific)
};



// Google OAuth Configuration
export const googleOAuthConfig = {
  clientId: process.env.GOOGLE_CLIENT_ID || '',
  clientSecret: process.env.GOOGLE_CLIENT_SECRET || '',
  redirectUri: process.env.GOOGLE_REDIRECT_URI || 'http://localhost:5173/google-calendar-callback',
  scopes: [
    'https://www.googleapis.com/auth/calendar',
    'https://www.googleapis.com/auth/calendar.events',
  ],
};

// Check if integrations are configured
export const isEmailConfigured = () => {
  return !!(
    emailConfig.host &&
    emailConfig.port &&
    emailConfig.auth.user &&
    emailConfig.auth.pass
  );
};

export const isResendConfigured = () => {
  return !!resendConfig.apiKey;
};



export const isGoogleOAuthConfigured = () => {
  return !!(
    googleOAuthConfig.clientId &&
    googleOAuthConfig.clientSecret &&
    googleOAuthConfig.redirectUri
  );
};
