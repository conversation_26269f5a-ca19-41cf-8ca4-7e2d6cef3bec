import express from 'express';
import {
  getCurrentSubscription,
  getSubscriptionTiers,
  subscribe,
  cancelUserSubscription
} from '../controllers/subscriptionController';
import { authenticate } from '../middleware/auth';
import { asyncHandler } from '../utils/routeHandler';

const router = express.Router();

// Get current user's subscription
router.get('/current', authenticate, asyncHandler(getCurrentSubscription));

// Get all available subscription tiers
router.get('/tiers', asyncHandler(getSubscriptionTiers));

// Subscribe to a new tier
router.post('/subscribe', authenticate, asyncHandler(subscribe));

// Cancel subscription
router.post('/cancel', authenticate, asyncHandler(cancelUserSubscription));



export default router;
