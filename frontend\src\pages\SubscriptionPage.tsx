import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useSubscription } from '../hooks/useSubscription';
import { TierLevel } from '../types/subscription';
import PricingTiers from '../components/subscription/PricingTiers';

import { Button } from '../components/ui/Button';
import { Card } from '../components/ui/Card';
import { Dialog } from '../components/ui/Dialog';
import { toast } from 'react-hot-toast';
import { formatDate } from '../utils/dateUtils';

const SubscriptionPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    subscription,
    isLoading,
    subscribe,
    cancelSubscription,
    isSubscriptionActive,
    daysRemaining
  } = useSubscription();

  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [selectedTier, setSelectedTier] = useState<TierLevel | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [cancelImmediately, setCancelImmediately] = useState(false);
  const [showPaymentForm, setShowPaymentForm] = useState(false);

  const handleSelectTier = (tierId: TierLevel) => {
    setSelectedTier(tierId);
    setIsUpgradeModalOpen(true);
  };

  const handleSubscribe = async () => {
    if (!selectedTier) return;

    try {
      setIsProcessing(true);

      // For free tier, no payment needed
      if (selectedTier === TierLevel.FREE) {
        await subscribe(selectedTier);
        toast.success('Subscription updated to Free tier!');
        setIsUpgradeModalOpen(false);
      } else {
        // For paid tiers, show payment form
        setShowPaymentForm(true);
      }
    } catch (error) {
      console.error('Error subscribing:', error);
      toast.error('Failed to update subscription. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePaymentSuccess = async (response: any) => {
    if (!selectedTier) return;

    try {
      setIsProcessing(true);

      if (response.success) {
        toast.success(`Subscription updated to ${selectedTier} tier!`);
        // Refresh subscription data
        await subscribe(selectedTier);
        setIsUpgradeModalOpen(false);
        setShowPaymentForm(false);
      } else {
        toast.error(response.message || 'Failed to process payment');
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      toast.error('Failed to process payment. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleCancelSubscription = async () => {
    try {
      setIsProcessing(true);
      await cancelSubscription(cancelImmediately);
      toast.success(
        cancelImmediately
          ? 'Subscription canceled immediately'
          : 'Subscription will be canceled at the end of the billing period'
      );
      setIsCancelModalOpen(false);
    } catch (error) {
      console.error('Error canceling subscription:', error);
      toast.error('Failed to cancel subscription. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-2xl font-bold">Subscription Management</h1>

      {subscription && (
        <Card className="mb-8 p-6">
          <h2 className="mb-4 text-xl font-semibold">Current Subscription</h2>
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <p className="text-sm text-gray-500">Plan</p>
              <p className="font-medium">
                {subscription.tier_id === TierLevel.FREE && 'Free'}
                {subscription.tier_id === TierLevel.PRO && 'Pro'}
                {subscription.tier_id === TierLevel.PRO_PLUS && 'Pro+'}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Status</p>
              <p className="font-medium capitalize">{subscription.status}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Current Period</p>
              <p className="font-medium">
                {formatDate(new Date(subscription.current_period_start))} to{' '}
                {formatDate(new Date(subscription.current_period_end))}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Auto Renew</p>
              <p className="font-medium">
                {subscription.cancel_at_period_end ? 'No' : 'Yes'}
              </p>
            </div>
          </div>

          {isSubscriptionActive() && subscription.tier_id !== TierLevel.FREE && (
            <div className="mt-6 flex flex-wrap gap-4">
              {!subscription.cancel_at_period_end && (
                <Button
                  variant="outline"
                  onClick={() => setIsCancelModalOpen(true)}
                >
                  Cancel Subscription
                </Button>
              )}
              {subscription.cancel_at_period_end && (
                <div className="rounded-md bg-yellow-50 p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-yellow-400"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">
                        Subscription Cancellation Scheduled
                      </h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          Your subscription will end on{' '}
                          {formatDate(new Date(subscription.current_period_end))}.
                          You have {daysRemaining} days remaining.
                        </p>
                      </div>
                      <div className="mt-4">
                        <Button
                          size="sm"
                          onClick={async () => {
                            try {
                              setIsProcessing(true);
                              await subscribe(subscription.tier_id);
                              toast.success('Subscription renewed successfully!');
                            } catch (error) {
                              console.error('Error renewing subscription:', error);
                              toast.error('Failed to renew subscription. Please try again.');
                            } finally {
                              setIsProcessing(false);
                            }
                          }}
                          disabled={isProcessing}
                        >
                          Resume Subscription
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </Card>
      )}



      <div className="mb-8">
        <h2 className="mb-4 text-xl font-semibold">Available Plans</h2>
        <PricingTiers onSelectTier={handleSelectTier} />
      </div>

      {/* Upgrade Modal */}
      <Dialog
        open={isUpgradeModalOpen}
        onClose={() => {
          setIsUpgradeModalOpen(false);
          setShowPaymentForm(false);
        }}
        title={showPaymentForm
          ? `Payment for ${selectedTier === TierLevel.PRO ? 'Pro' : 'Pro+'} Plan`
          : "Confirm Subscription Change"
        }
      >
        {!showPaymentForm ? (
          <>
            <div className="mt-2">
              <p className="text-sm text-gray-500">
                Are you sure you want to {subscription?.tier_id === selectedTier ? 'renew' : 'change'} your subscription to the{' '}
                {selectedTier === TierLevel.FREE && 'Free'}
                {selectedTier === TierLevel.PRO && 'Pro'}
                {selectedTier === TierLevel.PRO_PLUS && 'Pro+'} plan?
              </p>

              {selectedTier !== TierLevel.FREE && (
                <div className="mt-4 rounded-md bg-blue-50 p-3">
                  <p className="text-sm text-blue-700">
                    {selectedTier === TierLevel.PRO && 'Pro plan costs ₹399/month in India or $9.99/month globally.'}
                    {selectedTier === TierLevel.PRO_PLUS && 'Pro+ plan costs ₹999/month in India or $19.99/month globally.'}
                  </p>
                </div>
              )}
            </div>

            <div className="mt-4 flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={() => setIsUpgradeModalOpen(false)}
                disabled={isProcessing}
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubscribe}
                disabled={isProcessing}
              >
                {isProcessing ? 'Processing...' : 'Confirm'}
              </Button>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-600 mb-4">
              Payment integration is currently disabled. Subscription features are available for development and testing.
            </p>
            <div className="flex justify-center space-x-3">
              <Button
                variant="outline"
                onClick={() => setShowPaymentForm(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handlePaymentSuccess({ success: true })}
              >
                Activate Subscription (Dev Mode)
              </Button>
            </div>
          </div>
        )}
      </Dialog>

      {/* Cancel Modal */}
      <Dialog
        open={isCancelModalOpen}
        onClose={() => setIsCancelModalOpen(false)}
        title="Cancel Subscription"
      >
        <div className="mt-2">
          <p className="text-sm text-gray-500">
            Are you sure you want to cancel your subscription?
          </p>
          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={cancelImmediately}
                onChange={(e) => setCancelImmediately(e.target.checked)}
                className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">
                Cancel immediately (You will lose access to premium features)
              </span>
            </label>
          </div>
          {!cancelImmediately && (
            <p className="mt-2 text-sm text-gray-500">
              Your subscription will remain active until the end of the current billing period.
            </p>
          )}
        </div>

        <div className="mt-4 flex justify-end space-x-3">
          <Button
            variant="outline"
            onClick={() => setIsCancelModalOpen(false)}
            disabled={isProcessing}
          >
            Keep Subscription
          </Button>
          <Button
            variant="danger"
            onClick={handleCancelSubscription}
            disabled={isProcessing}
          >
            {isProcessing ? 'Processing...' : 'Cancel Subscription'}
          </Button>
        </div>
      </Dialog>
    </div>
  );
};

export default SubscriptionPage;
