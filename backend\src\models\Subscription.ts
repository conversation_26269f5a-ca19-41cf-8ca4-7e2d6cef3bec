import { TierLevel } from './SubscriptionTier';

export interface Subscription {
  id: string;
  user_id: string;
  tier_id: TierLevel;
  status: SubscriptionStatus;
  current_period_start: Date;
  current_period_end: Date;
  cancel_at_period_end: boolean;
  payment_method_id?: string;
  created_at: Date;
  updated_at: Date;
  metadata?: Record<string, any>;
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  CANCELED = 'canceled',
  INCOMPLETE = 'incomplete',
  INCOMPLETE_EXPIRED = 'incomplete_expired',
  PAST_DUE = 'past_due',
  TRIALING = 'trialing',
  UNPAID = 'unpaid'
}

// Helper functions for subscription management
export function isSubscriptionActive(subscription: Subscription | null): boolean {
  if (!subscription) return false;

  const activeStatuses: SubscriptionStatus[] = [
    SubscriptionStatus.ACTIVE,
    SubscriptionStatus.TRIALING
  ];

  return activeStatuses.includes(subscription.status) &&
    new Date(subscription.current_period_end) > new Date();
}

export function isSubscriptionExpired(subscription: Subscription | null): boolean {
  if (!subscription) return true;

  return new Date(subscription.current_period_end) <= new Date();
}

export function getSubscriptionDaysRemaining(subscription: Subscription | null): number {
  if (!subscription) return 0;

  const endDate = new Date(subscription.current_period_end);
  const now = new Date();

  const diffTime = endDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return Math.max(0, diffDays);
}
