import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout';
import { FiCheck, FiX } from 'react-icons/fi';

const Pricing: React.FC = () => {
  const [isIndianPricing, setIsIndianPricing] = useState(true);

  // Define pricing tiers
  const pricingTiers = [
    {
      name: 'Free',
      description: 'For individuals just getting started',
      priceIndia: '₹0',
      priceGlobal: '$0',
      features: [
        { name: 'Up to 5 appointments per month', included: true },
        { name: 'Basic calendar management', included: true },
        { name: 'Email notifications', included: true },
        { name: 'Single staff member', included: true },
        { name: 'Basic client management', included: true },
        { name: 'Standard booking page', included: true },
        { name: 'Google Calendar integration', included: false },
        { name: 'SMS notifications', included: false },
        { name: 'Custom branding', included: false },
        { name: 'Analytics & reporting', included: false },
        { name: 'Multiple staff members', included: false },
        { name: 'Priority support', included: false },
        { name: 'Virtual Appointments', included: false },
        { name: 'Video Conferencing', included: false },
      ],
      ctaText: 'Get Started',
      ctaLink: '/register',
      highlight: false,
    },
    {
      name: 'Pro',
      description: 'For growing businesses',
      priceIndia: '₹399',
      priceGlobal: '$9.99',
      features: [
        { name: 'Unlimited appointments', included: true },
        { name: 'Advanced calendar management', included: true },
        { name: 'Email notifications', included: true },
        { name: 'Up to 5 staff members', included: true },
        { name: 'Advanced client management', included: true },
        { name: 'Customizable booking page', included: true },
        { name: 'Google Calendar integration', included: true },
        { name: 'SMS notifications', included: true },
        { name: 'Custom branding', included: true },
        { name: 'Analytics & reporting', included: true },
        { name: 'Multiple staff members', included: true },
        { name: 'Priority support', included: false },
        { name: 'Virtual Appointments', included: false },
        { name: 'Video Conferencing', included: false },
      ],
      ctaText: 'Subscribe Now',
      ctaLink: '/register',
      highlight: true,
    },
    {
      name: 'Pro+',
      description: 'For large enterprises',
      priceIndia: '₹999',
      priceGlobal: '$19.99',
      features: [
        { name: 'Unlimited appointments', included: true },
        { name: 'Advanced calendar management', included: true },
        { name: 'Email notifications', included: true },
        { name: 'Unlimited staff members', included: true },
        { name: 'Advanced client management', included: true },
        { name: 'Customizable booking page', included: true },
        { name: 'Google Calendar integration', included: true },
        { name: 'SMS notifications', included: true },
        { name: 'Custom branding', included: true },
        { name: 'Analytics & reporting', included: true },
        { name: 'Multiple staff members', included: true },
        { name: 'Priority support', included: true },
        { name: 'Virtual Appointments', included: true },
        { name: 'Video Conferencing', included: true },
      ],
      ctaText: 'Subscribe Now',
      ctaLink: '/register',
      highlight: false,
    },
  ];

  return (
    <Layout>
      <div className="max-w-7xl mx-auto px-4 py-20 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Simple, Transparent <span className="text-blue-600">Pricing</span>
          </h1>
          <p className="text-lg text-gray-600 max-w-4xl mx-auto whitespace-nowrap">
            Choose the plan that's right for your business. All plans include a 14-day free trial.
          </p>

          {/* Pricing Toggle */}
          <div className="flex items-center justify-center mt-8">
            <span className={`mr-3 text-lg font-medium ${isIndianPricing ? 'font-bold text-blue-600' : 'text-gray-500'}`}>
              India (₹)
            </span>
            <button
              type="button"
              className={`relative inline-flex h-8 w-16 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                isIndianPricing ? 'bg-gray-200' : 'bg-blue-600'
              }`}
              role="switch"
              aria-checked={!isIndianPricing}
              onClick={() => setIsIndianPricing(!isIndianPricing)}
            >
              <span className="sr-only">Toggle pricing</span>
              <span
                aria-hidden="true"
                className={`pointer-events-none inline-block h-7 w-7 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  isIndianPricing ? 'translate-x-0' : 'translate-x-8'
                }`}
              />
            </button>
            <span className={`ml-3 text-lg font-medium ${!isIndianPricing ? 'font-bold text-blue-600' : 'text-gray-500'}`}>
              Global ($)
            </span>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto mt-20">
          {pricingTiers.map((tier) => (
            <div
              key={tier.name}
              className={`rounded-2xl overflow-hidden transition-all duration-300 transform hover:-translate-y-1 ${
                tier.highlight
                  ? 'border-2 border-blue-500 relative shadow-xl bg-blue-50 scale-105 z-10'
                  : 'border border-gray-200 shadow-lg'
              }`}
            >
              {tier.highlight && (
                <div className="bg-blue-600 text-white py-2 text-center text-sm font-bold tracking-wider uppercase">
                  Most Popular
                </div>
              )}
              <div className="p-8">
                <h3 className="text-2xl font-bold text-gray-900">{tier.name}</h3>
                <p className="mt-2 text-gray-600">{tier.description}</p>
                <p className="mt-6 flex items-baseline">
                  <span className={`text-5xl font-extrabold ${tier.highlight ? 'text-blue-700' : 'text-gray-900'}`}>
                    {isIndianPricing ? tier.priceIndia : tier.priceGlobal}
                  </span>
                  <span className="ml-1 text-xl font-semibold text-gray-500">/month</span>
                </p>
                <Link
                  to={tier.ctaLink}
                  className={`mt-8 block w-full py-3 px-6 rounded-md text-center font-medium transition-all ${
                    tier.highlight
                      ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-md hover:shadow-lg transform hover:-translate-y-1 text-lg py-4'
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  }`}
                >
                  {tier.ctaText}
                </Link>
              </div>
              <div className="px-8 pt-6 pb-8 bg-gray-50 border-t border-gray-100">
                <h4 className="text-sm font-semibold text-gray-900 tracking-wide uppercase">
                  What's included
                </h4>
                <ul className="mt-6 space-y-4">
                  {tier.features.map((feature) => (
                    <li key={feature.name} className="flex">
                      {feature.included ? (
                        <FiCheck className="flex-shrink-0 h-5 w-5 text-green-500" />
                      ) : (
                        <FiX className="flex-shrink-0 h-5 w-5 text-gray-400" />
                      )}
                      <span
                        className={`ml-3 text-base ${
                          feature.included ? 'text-gray-700' : 'text-gray-500'
                        }`}
                      >
                        {feature.name}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            Frequently Asked Questions
          </h2>
          <div className="max-w-4xl mx-auto grid gap-6">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Can I upgrade or downgrade my plan?
              </h3>
              <p className="text-gray-600">
                Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Do you offer a free trial?
              </h3>
              <p className="text-gray-600">
                Yes, all paid plans come with a 14-day free trial. No credit card required to start.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-md">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                How do I get started?
              </h3>
              <p className="text-gray-600">
                Simply sign up for a free account and start scheduling appointments immediately. You can upgrade to a paid plan anytime.
              </p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Pricing;
