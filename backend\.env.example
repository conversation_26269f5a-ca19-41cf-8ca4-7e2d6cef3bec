# Server Configuration
PORT=5000
NODE_ENV=development

# Frontend URL (for password reset links, etc.)
FRONTEND_URL=http://localhost:5173

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# JWT Configuration
JWT_SECRET=your_generated_jwt_secret
JWT_EXPIRES_IN=1d

# Legacy Email Configuration (Nodemailer)
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password
EMAIL_FROM=Scheduly <<EMAIL>>

# Resend Email Configuration
RESEND_API_KEY=your_resend_api_key
RESEND_FROM=Scheduly <<EMAIL>>
RESEND_REGION=us



# Google OAuth Configuration (for Calendar)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=http://localhost:5173/google-calendar-callback
