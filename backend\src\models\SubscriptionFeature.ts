// Define all possible subscription features
export type SubscriptionFeature = 
  // Calendar Management
  | 'basicCalendarManagement'
  | 'advancedCalendarManagement'
  
  // Client Management
  | 'basicClientManagement'
  | 'advancedClientManagement'
  
  // Booking Page
  | 'standardBookingPage'
  | 'customizableBookingPage'
  
  // Notifications
  | 'emailNotifications'
  
  // Integrations
  | 'googleCalendarIntegration'
  
  // Branding and Customization
  | 'customBranding'
  
  // Analytics
  | 'analyticsAndReporting'
  
  // Staff Management
  | 'multipleStaffMembers'
  
  // Support
  | 'prioritySupport'
  
  // Advanced Features
  | 'virtualAppointments'
  | 'videoConferencing';

// Feature descriptions for UI display
export const FEATURE_DESCRIPTIONS: Record<SubscriptionFeature, string> = {
  // Calendar Management
  basicCalendarManagement: 'Basic calendar with fixed appointment slots and simple scheduling',
  advancedCalendarManagement: 'Advanced calendar with custom durations, buffer times, and multiple views',
  
  // Client Management
  basicClientManagement: 'Simple client profiles with basic contact information',
  advancedClientManagement: 'Comprehensive client profiles with history, custom fields, and segmentation',
  
  // Booking Page
  standardBookingPage: 'Standard booking page with basic customization',
  customizableBookingPage: 'Fully customizable booking page with your branding and layout options',
  
  // Notifications
  emailNotifications: 'Email notifications for appointments and reminders',
  smsNotifications: 'SMS notifications for appointments and reminders',
  
  // Integrations
  googleCalendarIntegration: 'Sync with Google Calendar',
  
  // Branding and Customization
  customBranding: 'Remove Scheduly branding and add your own',
  
  // Analytics
  analyticsAndReporting: 'Detailed analytics and reporting on appointments and clients',
  
  // Staff Management
  multipleStaffMembers: 'Add multiple staff members with individual calendars',
  
  // Support
  prioritySupport: 'Priority customer support with faster response times',
  
  // Advanced Features
  virtualAppointments: 'Host virtual appointments with clients',
  videoConferencing: 'Built-in video conferencing for virtual appointments'
};
