# Scheduly Documentation

This document provides comprehensive documentation for the Scheduly application, including setup instructions, troubleshooting guides, and integration details.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Third-Party Integrations](#third-party-integrations)
3. [Troubleshooting](#troubleshooting)
   - [Backend Issues](#backend-issues)
   - [Database Connection Issues](#database-connection-issues)
4. [Development Guidelines](#development-guidelines)

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- PostgreSQL (v12 or higher)
- Docker (optional, for containerized database)

### Installation

1. Clone the repository
```bash
git clone https://github.com/yourusername/scheduly.git
cd scheduly
```

2. Install backend dependencies
```bash
cd backend
npm install
```

3. Install frontend dependencies
```bash
cd ../frontend
npm install
```

4. Configure environment variables
   - Copy `.env.example` to `.env` in both backend and frontend directories
   - Update the values in the `.env` files with your configuration

5. Start the development servers

Backend:
```bash
# In the backend directory
npm run dev
```

Frontend:
```bash
# In the frontend directory
npm run dev
```

## Third-Party Integrations

### Google Calendar Integration

The application integrates with Google Calendar to sync appointments. To set up this integration:

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project
3. Enable the Google Calendar API
4. Create OAuth 2.0 credentials
5. Add the following environment variables to your `.env` file:

```
GOOGLE_CLIENT_ID=your_client_id
GOOGLE_CLIENT_SECRET=your_client_secret
GOOGLE_REDIRECT_URI=http://localhost:5173/google-calendar-callback
```

### Email Notifications

The application uses Nodemailer to send email notifications. To set up email notifications:

1. Choose an email service provider (e.g., Gmail, SendGrid, Mailgun)
2. Add the following environment variables to your `.env` file:

```
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password
EMAIL_FROM=Scheduly <<EMAIL>>
```

#### Using Gmail

If you're using Gmail, use these settings:

```
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_app_password
```

Note: For Gmail, you'll need to create an "App Password" in your Google Account security settings.



## Troubleshooting

### Backend Issues

If you're experiencing issues with the backend not running, here are some steps to troubleshoot and fix the problems:

#### 1. Check Dependencies

Make sure all required dependencies are installed:

```bash
cd backend
npm install
```

#### 2. Check Database Connection

Make sure PostgreSQL is running:

```bash
docker ps
```

If you don't see the PostgreSQL container running, start it:

```bash
docker-compose up -d
```

#### 3. Check Environment Variables

Make sure the `.env` file in the backend directory has the correct configuration.

#### 4. Try Running with ts-node Directly

If nodemon is causing issues, try running the server directly with ts-node:

```bash
cd backend
npx ts-node src/server.ts
```

#### 5. Fix TypeScript Errors

Run the TypeScript compiler to check for any errors:

```bash
cd backend
npx tsc --noEmit
```

Or use our fix script:

```bash
cd backend
npm run fix
```

#### 6. Check for Port Conflicts

Make sure no other application is using port 5000. You can change the port in the `.env` file if needed.

### Database Connection Issues

If you're experiencing issues with the database connection, here are some steps to troubleshoot:

#### 1. Check PostgreSQL Container

Make sure the PostgreSQL container is running:

```bash
docker ps
```

If you don't see the PostgreSQL container running, start it:

```bash
docker-compose up -d
```

#### 2. Check Database Configuration

Make sure the `.env` file in the backend directory has the correct database configuration:

```
DB_HOST=localhost
DB_PORT=5432
DB_NAME=scheduly
DB_USER=postgres
DB_PASSWORD=postgres
```

#### 3. Force Recreate Database Tables

If you're still having issues, you can try forcing the recreation of the database tables by modifying the `sequelize.sync()` call in `server.ts`:

```typescript
await sequelize.sync({ force: true });
```

**Warning**: This will drop all tables and recreate them, so use with caution in development environments only.

#### 4. Connect to PostgreSQL Directly

Try connecting to PostgreSQL directly to check if it's running correctly:

```bash
docker exec -it scheduly-postgres psql -U postgres
```

Once connected, you can check if the database exists:

```sql
\l
```

And create it if it doesn't exist:

```sql
CREATE DATABASE scheduly;
```

## Development Guidelines

### Code Style

- Use TypeScript for type safety
- Follow ESLint rules for code formatting
- Use async/await for asynchronous operations
- Use meaningful variable and function names

### Git Workflow

- Create feature branches from `main`
- Use descriptive commit messages
- Create pull requests for code reviews
- Merge to `main` after approval

### Testing

- Write unit tests for backend services and controllers
- Write integration tests for API endpoints
- Write component tests for frontend components
- Run tests before committing code
