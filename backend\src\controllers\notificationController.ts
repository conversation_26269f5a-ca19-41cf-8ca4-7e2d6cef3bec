import { Request, Response } from 'express';
import {
  NotificationType,
  NotificationChannel,
  AppointmentStatus,
  UserRole
} from '../models/supabase';
import {
  Notification,
  Appointment,
  User,
  Service,
  Tenant
} from '../utils/modelHelpers';
import { sendAppointmentReminder as sendLegacyReminderEmail } from '../utils/email';
import { sendAppointmentReminder as sendReminderEmail } from '../utils/resendEmail';

import { sendAppointmentReminders as sendScheduledReminders } from '../utils/scheduler';
import { Op } from '../utils/queryOperators';


// Get notifications for a user
export const getUserNotifications = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId, userId } = req.params;

    // Check if user has access to this tenant
    if (req.user.tenantId !== tenantId) {
      return res.status(403).json({ message: 'Not authorized to access notifications for this tenant' });
    }

    // Check if user is requesting their own notifications or has admin access
    if (req.user.id !== userId && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to access notifications for this user' });
    }

    const notifications = await Notification.findAll({
      where: {
        userId,
      },
      include: [
        {
          model: Appointment,
          as: 'appointment',
          include: [
            {
              model: Service,
              as: 'service',
            },
            {
              model: User,
              as: 'staff',
              attributes: ['id', 'firstName', 'lastName'],
            },
          ],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    res.json(notifications);
  } catch (error) {
    console.error('Get user notifications error:', error);
    res.status(500).json({ message: 'Failed to get user notifications' });
  }
};

// Mark notification as read
export const markNotificationAsRead = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId, notificationId } = req.params;

    // Check if user has access to this tenant
    if (req.user.tenantId !== tenantId) {
      return res.status(403).json({ message: 'Not authorized to access notifications for this tenant' });
    }

    const notification = await Notification.findByPk(notificationId);

    if (!notification) {
      return res.status(404).json({ message: 'Notification not found' });
    }

    // Check if user owns the notification or has admin access
    if (notification.userId !== req.user.id && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to update this notification' });
    }

    // Mark as read (in a real implementation, you would have a 'read' field)
    // For now, we'll just return success
    res.json({ message: 'Notification marked as read' });
  } catch (error) {
    console.error('Mark notification as read error:', error);
    res.status(500).json({ message: 'Failed to mark notification as read' });
  }
};

// Send appointment reminders
export const sendAppointmentReminders = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.params;

    // Check if user has admin access to this tenant
    if (req.user.tenantId !== tenantId || req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to send reminders for this tenant' });
    }

    // Get tomorrow's date
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    const tomorrowEnd = new Date(tomorrow);
    tomorrowEnd.setHours(23, 59, 59, 999);

    // Get appointments for tomorrow that haven't had reminders sent
    const appointments = await Appointment.findAll({
      where: {
        tenantId,
        startTime: {
          [Op.between]: [tomorrow, tomorrowEnd],
        },
        status: AppointmentStatus.CONFIRMED,
        reminderSent: false,
      },
      include: [
        {
          model: User,
          as: 'client',
        },
        {
          model: User,
          as: 'staff',
        },
        {
          model: Service,
          as: 'service',
        },
        {
          model: Tenant,
          as: 'tenant',
        },
      ],
    });

    // Send reminders
    const results = await Promise.all(
      appointments.map(async (appointment) => {
        try {
          // Use type assertion to access the associations
          const appointmentWithAssociations = appointment as any;

          // Send email reminder
          const emailSent = await sendReminderEmail(
            appointment,
            appointmentWithAssociations.client,
            appointmentWithAssociations.staff,
            appointmentWithAssociations.service,
            appointmentWithAssociations.tenant
          );

          // Create notification record for email
          await Notification.create({
            userId: appointmentWithAssociations.client.id,
            appointmentId: appointment.id,
            type: NotificationType.APPOINTMENT_REMINDER,
            channel: NotificationChannel.EMAIL,
            content: `Reminder for your appointment for ${appointmentWithAssociations.service.name} with ${appointmentWithAssociations.staff.firstName} ${appointmentWithAssociations.staff.lastName} tomorrow at ${appointment.startTime.toLocaleTimeString()}`,
            sent: emailSent,
            sentAt: emailSent ? new Date() : undefined,
            error: emailSent ? undefined : 'Failed to send email reminder',
          });



          // Mark appointment as having had reminders sent
          await Appointment.update(
            { reminderSent: true },
            {
              where: {
                id: appointment.id
              }
            }
          );

          return {
            appointmentId: appointment.id,
            clientId: appointmentWithAssociations.client.id,
            emailSent,
          };
        } catch (error) {
          console.error(`Failed to send reminder for appointment ${appointment.id}:`, error);
          // Use type assertion to access the associations even in the error case
          const appointmentWithAssociations = appointment as any;
          return {
            appointmentId: appointment.id,
            clientId: appointmentWithAssociations.client.id,
            emailSent: false,
            error: (error as Error).message,
          };
        }
      })
    );

    res.json({
      message: `Sent reminders for ${appointments.length} appointments`,
      results,
    });
  } catch (error) {
    console.error('Send appointment reminders error:', error);
    res.status(500).json({ message: 'Failed to send appointment reminders' });
  }
};

// Send appointment reminders for all tenants (for testing the scheduler)
export const sendAllAppointmentReminders = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Check if user has admin role
    if (req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to send reminders for all tenants' });
    }

    // Call the scheduler function
    await sendScheduledReminders();

    res.json({
      message: 'Appointment reminders sent for all tenants',
    });
  } catch (error) {
    console.error('Send all appointment reminders error:', error);
    res.status(500).json({ message: 'Failed to send appointment reminders for all tenants' });
  }
};