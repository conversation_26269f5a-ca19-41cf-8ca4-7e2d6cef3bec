import { SubscriptionFeature } from './SubscriptionFeature';

export enum TierLevel {
  FREE = 'free',
  PRO = 'pro',
  PRO_PLUS = 'pro_plus'
}

export interface TierPricing {
  india: number; // Price in INR
  global: number; // Price in USD
}



export interface SubscriptionTier {
  id: TierLevel;
  name: string;
  description: string;
  pricing: TierPricing;
  features: SubscriptionFeature[];
  maxAppointmentsPerMonth: number | null; // null means unlimited
  maxStaffMembers: number | null; // null means unlimited
  trialDays: number;
}

// Define the tiers with their features
export const SUBSCRIPTION_TIERS: SubscriptionTier[] = [
  {
    id: TierLevel.FREE,
    name: 'Free',
    description: 'For individuals just getting started',
    pricing: {
      india: 0,
      global: 0
    },

    features: [
      'basicCalendarManagement',
      'emailNotifications',
      'basicClientManagement',
      'standardBookingPage'
    ],
    maxAppointmentsPerMonth: 5,
    maxStaffMembers: 1,
    trialDays: 0
  },
  {
    id: TierLevel.PRO,
    name: 'Pro',
    description: 'For growing businesses',
    pricing: {
      india: 399,
      global: 9.99
    },

    features: [
      'advancedCalendarManagement',
      'emailNotifications',
      'advancedClientManagement',
      'customizableBookingPage',
      'googleCalendarIntegration',
      'customBranding',
      'analyticsAndReporting',
      'multipleStaffMembers'
    ],
    maxAppointmentsPerMonth: null, // unlimited
    maxStaffMembers: 5,
    trialDays: 14
  },
  {
    id: TierLevel.PRO_PLUS,
    name: 'Pro+',
    description: 'For large enterprises',
    pricing: {
      india: 999,
      global: 19.99
    },

    features: [
      'advancedCalendarManagement',
      'emailNotifications',
      'advancedClientManagement',
      'customizableBookingPage',
      'googleCalendarIntegration',
      'customBranding',
      'analyticsAndReporting',
      'multipleStaffMembers',
      'prioritySupport',
      'virtualAppointments'
      // 'videoConferencing' - Not implementing as per requirements
    ],
    maxAppointmentsPerMonth: null, // unlimited
    maxStaffMembers: null, // unlimited
    trialDays: 14
  }
];

// Helper function to check if a tier has a specific feature
export function tierHasFeature(tierId: TierLevel, featureId: string): boolean {
  const tier = SUBSCRIPTION_TIERS.find(t => t.id === tierId);
  return tier ? tier.features.includes(featureId as any) : false;
}

// Helper function to get the tier by ID
export function getTierById(tierId: TierLevel): SubscriptionTier | undefined {
  return SUBSCRIPTION_TIERS.find(t => t.id === tierId);
}
