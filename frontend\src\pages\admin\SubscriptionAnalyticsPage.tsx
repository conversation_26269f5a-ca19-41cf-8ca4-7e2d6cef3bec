import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../../utils/api';
import { Button } from '../../components/ui/Button';
import { Card } from '../../components/ui/Card';
import { TierLevel, SubscriptionStatus } from '../../types/subscription';
import { FiDownload, FiCalendar } from 'react-icons/fi';
import { toast } from 'react-hot-toast';
import Layout from '../../components/Layout';

interface AnalyticsData {
  tierCounts: {
    tier_id: TierLevel;
    count: number;
  }[];
  statusCounts: {
    status: SubscriptionStatus;
    count: number;
  }[];
  monthlySubscriptions: Record<string, number>;
  revenue: {
    monthly: Record<string, number>;
    mrr: number;
    arr: number;
  };
}

const SubscriptionAnalyticsPage: React.FC = () => {
  const navigate = useNavigate();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<{start?: string; end?: string}>({});

  // Fetch analytics data
  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      let url = '/admin/subscription/analytics';

      // Add date range parameters if provided
      if (dateRange.start || dateRange.end) {
        url += '?';
        if (dateRange.start) {
          url += `startDate=${dateRange.start}`;
        }

        if (dateRange.end) {
          url += `${dateRange.start ? '&' : ''}endDate=${dateRange.end}`;
        }
      }

      const response = await api.get(url);

      if (response.data.success) {
        setAnalyticsData(response.data.data);
      } else {
        setError(response.data.message || 'Failed to fetch analytics data');
      }
    } catch (err: any) {
      console.error('Error fetching analytics data:', err);
      setError(err.response?.data?.message || err.message || 'An error occurred while fetching analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchAnalyticsData();
  }, [dateRange.start, dateRange.end]);

  // Format month-year for display
  const formatMonthYear = (monthYear: string) => {
    const [year, month] = monthYear.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
  };

  // Calculate total subscriptions
  const getTotalSubscriptions = () => {
    if (!analyticsData?.tierCounts) return 0;
    return analyticsData.tierCounts.reduce((total, item) => total + item.count, 0);
  };

  // Calculate percentage for a count
  const calculatePercentage = (count: number) => {
    const total = getTotalSubscriptions();
    if (total === 0) return 0;
    return Math.round((count / total) * 100);
  };

  // Get color for tier
  const getTierColor = (tier: TierLevel) => {
    switch (tier) {
      case TierLevel.FREE:
        return 'bg-gray-100 text-gray-800';
      case TierLevel.PRO:
        return 'bg-blue-100 text-blue-800';
      case TierLevel.PRO_PLUS:
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get color for status
  const getStatusColor = (status: SubscriptionStatus) => {
    switch (status) {
      case SubscriptionStatus.ACTIVE:
        return 'bg-green-100 text-green-800';
      case SubscriptionStatus.TRIALING:
        return 'bg-blue-100 text-blue-800';
      case SubscriptionStatus.CANCELED:
        return 'bg-red-100 text-red-800';
      case SubscriptionStatus.PAST_DUE:
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get tier name
  const getTierName = (tier: TierLevel) => {
    switch (tier) {
      case TierLevel.FREE:
        return 'Free';
      case TierLevel.PRO:
        return 'Pro';
      case TierLevel.PRO_PLUS:
        return 'Pro+';
      default:
        return tier;
    }
  };

  // Export analytics data to CSV
  const exportToCSV = () => {
    if (!analyticsData) {
      toast.error('No data to export');
      return;
    }

    // Create CSV content for tier counts
    const tierHeaders = ['Tier', 'Count', 'Percentage'];
    const tierRows = analyticsData.tierCounts.map(item => [
      getTierName(item.tier_id),
      item.count.toString(),
      `${calculatePercentage(item.count)}%`
    ]);

    // Create CSV content for status counts
    const statusHeaders = ['Status', 'Count', 'Percentage'];
    const statusRows = analyticsData.statusCounts.map(item => [
      item.status,
      item.count.toString(),
      `${calculatePercentage(item.count)}%`
    ]);

    // Create CSV content for monthly subscriptions
    const monthlyHeaders = ['Month', 'New Subscriptions'];
    const monthlyRows = Object.entries(analyticsData.monthlySubscriptions)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([monthYear, count]) => [
        formatMonthYear(monthYear),
        count.toString()
      ]);

    // Combine all sections
    const csvContent = [
      'Subscription Analytics Export',
      `Generated on: ${new Date().toLocaleDateString()}`,
      '',
      'Subscriptions by Tier',
      tierHeaders.join(','),
      ...tierRows.map(row => row.join(',')),
      '',
      'Subscriptions by Status',
      statusHeaders.join(','),
      ...statusRows.map(row => row.join(',')),
      '',
      'Monthly New Subscriptions',
      monthlyHeaders.join(','),
      ...monthlyRows.map(row => row.join(','))
    ].join('\n');

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `subscription_analytics_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast.success('Export successful');
  };

  return (
    <Layout>
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold">Subscription Analytics</h1>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={exportToCSV}
            disabled={!analyticsData}
          >
            <FiDownload className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
          <Button onClick={() => navigate('/admin/subscription')}>
            Back to Subscriptions
          </Button>
        </div>
      </div>

      {/* Date Range Filter */}
      <Card className="mb-6 p-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              Start Date
            </label>
            <input
              type="date"
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              value={dateRange.start || ''}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
            />
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">
              End Date
            </label>
            <input
              type="date"
              className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm"
              value={dateRange.end || ''}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
            />
          </div>

          <div className="flex items-end">
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setDateRange({})}
            >
              <FiCalendar className="mr-2 h-4 w-4" />
              Reset Date Range
            </Button>
          </div>
        </div>
      </Card>

      {isLoading && (
        <div className="flex h-64 items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-blue-600"></div>
        </div>
      )}

      {error && (
        <div className="rounded-md bg-red-50 p-4 text-sm text-red-700">
          <p>{error}</p>
        </div>
      )}

      {!isLoading && !error && analyticsData && (
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* Subscription by Tier */}
          <Card className="p-6">
            <h2 className="mb-4 text-lg font-semibold">Subscriptions by Tier</h2>
            <div className="space-y-4">
              {analyticsData.tierCounts.map((item) => (
                <div key={item.tier_id}>
                  <div className="mb-1 flex items-center justify-between">
                    <div className="flex items-center">
                      <span className={`mr-2 rounded-full px-2 py-1 text-xs font-medium ${getTierColor(item.tier_id)}`}>
                        {getTierName(item.tier_id)}
                      </span>
                      <span className="text-sm text-gray-500">
                        {item.count} subscriptions
                      </span>
                    </div>
                    <span className="text-sm font-medium">
                      {calculatePercentage(item.count)}%
                    </span>
                  </div>
                  <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                    <div
                      className={`h-full rounded-full ${
                        item.tier_id === TierLevel.FREE
                          ? 'bg-gray-500'
                          : item.tier_id === TierLevel.PRO
                          ? 'bg-blue-500'
                          : 'bg-purple-500'
                      }`}
                      style={{ width: `${calculatePercentage(item.count)}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Subscription by Status */}
          <Card className="p-6">
            <h2 className="mb-4 text-lg font-semibold">Subscriptions by Status</h2>
            <div className="space-y-4">
              {analyticsData.statusCounts.map((item) => (
                <div key={item.status}>
                  <div className="mb-1 flex items-center justify-between">
                    <div className="flex items-center">
                      <span className={`mr-2 rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(item.status)}`}>
                        {item.status}
                      </span>
                      <span className="text-sm text-gray-500">
                        {item.count} subscriptions
                      </span>
                    </div>
                    <span className="text-sm font-medium">
                      {calculatePercentage(item.count)}%
                    </span>
                  </div>
                  <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                    <div
                      className={`h-full rounded-full ${
                        item.status === SubscriptionStatus.ACTIVE
                          ? 'bg-green-500'
                          : item.status === SubscriptionStatus.TRIALING
                          ? 'bg-blue-500'
                          : item.status === SubscriptionStatus.CANCELED
                          ? 'bg-red-500'
                          : 'bg-yellow-500'
                      }`}
                      style={{ width: `${calculatePercentage(item.count)}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Monthly Subscriptions */}
          <Card className="p-6 md:col-span-2">
            <h2 className="mb-4 text-lg font-semibold">Monthly New Subscriptions</h2>
            {Object.keys(analyticsData.monthlySubscriptions).length === 0 ? (
              <p className="text-center text-gray-500">No subscription data available</p>
            ) : (
              <div className="h-64">
                <div className="flex h-full items-end space-x-2">
                  {Object.entries(analyticsData.monthlySubscriptions)
                    .sort(([a], [b]) => a.localeCompare(b))
                    .map(([monthYear, count]) => {
                      const maxCount = Math.max(...Object.values(analyticsData.monthlySubscriptions));
                      const height = maxCount > 0 ? (count / maxCount) * 100 : 0;

                      return (
                        <div
                          key={monthYear}
                          className="flex flex-1 flex-col items-center"
                        >
                          <div className="relative w-full">
                            <div
                              className="w-full rounded-t bg-blue-500"
                              style={{ height: `${height}%` }}
                            ></div>
                            <div className="absolute bottom-0 left-0 right-0 flex justify-center">
                              <span className="mt-1 text-xs font-medium text-gray-700">
                                {count}
                              </span>
                            </div>
                          </div>
                          <div className="mt-2 text-xs text-gray-500">
                            {formatMonthYear(monthYear)}
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>
            )}
          </Card>

          {/* Summary Card */}
          <Card className="p-6 md:col-span-2">
            <h2 className="mb-4 text-lg font-semibold">Summary</h2>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
              <div className="rounded-lg bg-gray-50 p-4">
                <h3 className="text-sm font-medium text-gray-500">Total Subscriptions</h3>
                <p className="mt-1 text-2xl font-semibold text-gray-900">
                  {getTotalSubscriptions()}
                </p>
              </div>

              <div className="rounded-lg bg-blue-50 p-4">
                <h3 className="text-sm font-medium text-blue-700">Active Subscriptions</h3>
                <p className="mt-1 text-2xl font-semibold text-blue-900">
                  {analyticsData.statusCounts.find(s => s.status === SubscriptionStatus.ACTIVE)?.count || 0}
                </p>
              </div>

              <div className="rounded-lg bg-green-50 p-4">
                <h3 className="text-sm font-medium text-green-700">Paid Subscriptions</h3>
                <p className="mt-1 text-2xl font-semibold text-green-900">
                  {(analyticsData.tierCounts.find(t => t.tier_id === TierLevel.PRO)?.count || 0) +
                   (analyticsData.tierCounts.find(t => t.tier_id === TierLevel.PRO_PLUS)?.count || 0)}
                </p>
              </div>

              <div className="rounded-lg bg-purple-50 p-4">
                <h3 className="text-sm font-medium text-purple-700">Pro+ Subscriptions</h3>
                <p className="mt-1 text-2xl font-semibold text-purple-900">
                  {analyticsData.tierCounts.find(t => t.tier_id === TierLevel.PRO_PLUS)?.count || 0}
                </p>
              </div>
            </div>
          </Card>

          {/* Revenue Analytics */}
          <Card className="p-6 md:col-span-2">
            <h2 className="mb-4 text-lg font-semibold">Revenue Analytics</h2>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <div className="rounded-lg bg-green-50 p-4">
                <h3 className="text-sm font-medium text-green-700">Monthly Recurring Revenue (MRR)</h3>
                <p className="mt-1 text-2xl font-semibold text-green-900">
                  ₹{analyticsData.revenue.mrr.toLocaleString()}
                </p>
              </div>

              <div className="rounded-lg bg-blue-50 p-4">
                <h3 className="text-sm font-medium text-blue-700">Annual Recurring Revenue (ARR)</h3>
                <p className="mt-1 text-2xl font-semibold text-blue-900">
                  ₹{analyticsData.revenue.arr.toLocaleString()}
                </p>
              </div>
            </div>

            {/* Monthly Revenue Chart */}
            <div className="mt-6">
              <h3 className="mb-4 text-sm font-medium text-gray-700">Monthly Revenue</h3>
              {Object.keys(analyticsData.revenue.monthly).length === 0 ? (
                <p className="text-center text-gray-500">No revenue data available</p>
              ) : (
                <div className="h-64">
                  <div className="flex h-full items-end space-x-2">
                    {Object.entries(analyticsData.revenue.monthly)
                      .sort(([a], [b]) => a.localeCompare(b))
                      .map(([monthYear, amount]) => {
                        const maxAmount = Math.max(...Object.values(analyticsData.revenue.monthly));
                        const height = maxAmount > 0 ? (amount / maxAmount) * 100 : 0;

                        return (
                          <div
                            key={monthYear}
                            className="flex flex-1 flex-col items-center"
                          >
                            <div className="relative w-full">
                              <div
                                className="w-full rounded-t bg-green-500"
                                style={{ height: `${height}%` }}
                              ></div>
                              <div className="absolute bottom-0 left-0 right-0 flex justify-center">
                                <span className="mt-1 text-xs font-medium text-gray-700">
                                  ₹{amount.toLocaleString()}
                                </span>
                              </div>
                            </div>
                            <div className="mt-2 text-xs text-gray-500">
                              {formatMonthYear(monthYear)}
                            </div>
                          </div>
                        );
                      })}
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>
      )}
    </Layout>
  );
};

export default SubscriptionAnalyticsPage;
