// This file provides helper functions to make the transition from Sequelize to Supabase easier
import { supabase } from '../config/supabase';
import config from '../config';
import type {
  Tenant as TenantType, TenantDB as TenantDBType,
  User as UserType, UserDB as UserDBType,
  Service as ServiceType, ServiceDB as ServiceDBType,
  StaffService as StaffServiceType, StaffServiceDB as StaffServiceDBType,
  Availability as AvailabilityType, AvailabilityDB as AvailabilityDBType,
  TimeOff as TimeOffType, TimeOffDB as TimeOffDBType,
  Appointment as AppointmentType, AppointmentDB as AppointmentDBType,
  Notification as NotificationType, NotificationDB as NotificationDBType
} from '../models/supabase';

// Check if Supabase credentials are available
const isSupabaseConfigured = !!(config.supabase.url && (config.supabase.anonKey || config.supabase.serviceKey));
if (!isSupabaseConfigured) {
  console.error('Supabase is not properly configured. Please check your environment variables.');
  console.error('Required: config.supabase.url and either config.supabase.anonKey or config.supabase.serviceKey');
}

// Type for query options
export interface QueryOptions {
  where?: Record<string, any>;
  include?: Array<{
    model: string | any; // Allow model objects or strings
    as?: string;
    attributes?: string[];
    include?: Array<{
      model: string | any;
      as?: string;
      attributes?: string[];
    }>;
  }>;
  order?: Array<[string, 'ASC' | 'DESC']>;
  limit?: number;
  offset?: number;
  attributes?: string[];
}

/**
 * Helper function to convert snake_case to camelCase
 * @param obj The object to convert
 * @returns The converted object with camelCase keys
 */
const toCamelCase = <T = any>(obj: any): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => toCamelCase<any>(item)) as unknown as T;
  }

  // Convert date strings to Date objects
  const isIsoDateString = (str: string): boolean => {
    if (typeof str !== 'string') return false;
    return /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z$/.test(str);
  };

  return Object.keys(obj).reduce((acc: any, key) => {
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());

    // Convert ISO date strings to Date objects
    if (typeof obj[key] === 'string' && isIsoDateString(obj[key])) {
      acc[camelKey] = new Date(obj[key]);
    } else {
      acc[camelKey] = toCamelCase(obj[key]);
    }

    return acc;
  }, {}) as T;
};

/**
 * Helper function to convert camelCase to snake_case
 * @param obj The object to convert
 * @returns The converted object with snake_case keys
 */
const toSnakeCase = <T = any>(obj: any): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => toSnakeCase<any>(item)) as unknown as T;
  }

  return Object.keys(obj).reduce((acc: any, key) => {
    // Skip functions and symbols
    if (typeof obj[key] === 'function' || typeof obj[key] === 'symbol') {
      return acc;
    }

    const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);

    // Convert Date objects to ISO strings for Supabase
    if (obj[key] instanceof Date) {
      acc[snakeKey] = obj[key].toISOString();
    } else {
      acc[snakeKey] = toSnakeCase(obj[key]);
    }

    return acc;
  }, {}) as T;
};

// Helper function to create a model with standard CRUD operations
const createModel = <T, TDB>(tableName: string) => {
  return {
    findByPk: async (id: string): Promise<T | null> => {
      try {
        if (!isSupabaseConfigured) {
          console.error(`Cannot find ${tableName} by primary key: Supabase is not configured`);
          return null;
        }

        const { data, error } = await supabase
          .from(tableName)
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          console.error(`Error finding ${tableName} by primary key:`, error);
          return null;
        }

        return data ? toCamelCase<T>(data) : null;
      } catch (err) {
        console.error(`Unexpected error finding ${tableName} by primary key:`, err);
        return null;
      }
    },
    findOne: async (options: QueryOptions): Promise<T | null> => {
      try {
        if (!isSupabaseConfigured) {
          console.error(`Cannot find one ${tableName}: Supabase is not configured`);
          return null;
        }

        let query = supabase.from(tableName).select('*');

        if (options.where) {
          Object.entries(toSnakeCase(options.where)).forEach(([key, value]) => {
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
              // Handle operators
              Object.entries(value).forEach(([opKey, opValue]) => {
                if (opKey.includes('Op.')) {
                  const operator = opKey.replace('[Op.', '').replace(']', '');
                  switch (operator) {
                    case 'eq': query = query.eq(key, opValue as any); break;
                    case 'ne': query = query.neq(key, opValue as any); break;
                    case 'gt': query = query.gt(key, opValue as any); break;
                    case 'gte': query = query.gte(key, opValue as any); break;
                    case 'lt': query = query.lt(key, opValue as any); break;
                    case 'lte': query = query.lte(key, opValue as any); break;
                    case 'in': query = query.in(key, opValue as any[]); break;
                    case 'notIn': query = query.not(key, 'in', opValue as any[]); break;
                    case 'not': query = query.not(key, 'eq', opValue as any); break;
                    default: query = query.eq(key, opValue as any);
                  }
                } else {
                  query = query.eq(key, opValue as any);
                }
              });
            } else {
              query = query.eq(key, value as any);
            }
          });
        }

        const { data, error } = await query.single();

        if (error) {
          // Don't log "No rows found" errors as they're expected
          if (!error.message.includes('No rows found')) {
            console.error(`Error finding one ${tableName}:`, error);
          }
          return null;
        }

        return data ? toCamelCase<T>(data) : null;
      } catch (err) {
        console.error(`Unexpected error finding one ${tableName}:`, err);
        return null;
      }
    },
    findAll: async (options: QueryOptions = {}): Promise<T[]> => {
      try {
        if (!isSupabaseConfigured) {
          console.error(`Cannot find all ${tableName}: Supabase is not configured`);
          return [];
        }

        let query = supabase.from(tableName).select('*');

        if (options.where) {
          Object.entries(toSnakeCase(options.where)).forEach(([key, value]) => {
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
              // Handle operators
              Object.entries(value).forEach(([opKey, opValue]) => {
                if (opKey.includes('Op.')) {
                  const operator = opKey.replace('[Op.', '').replace(']', '');
                  switch (operator) {
                    case 'eq': query = query.eq(key, opValue as any); break;
                    case 'ne': query = query.neq(key, opValue as any); break;
                    case 'gt': query = query.gt(key, opValue as any); break;
                    case 'gte': query = query.gte(key, opValue as any); break;
                    case 'lt': query = query.lt(key, opValue as any); break;
                    case 'lte': query = query.lte(key, opValue as any); break;
                    case 'in': query = query.in(key, opValue as any[]); break;
                    case 'notIn': query = query.not(key, 'in', opValue as any[]); break;
                    case 'not': query = query.not(key, 'eq', opValue as any); break;
                    default: query = query.eq(key, opValue as any);
                  }
                } else {
                  query = query.eq(key, opValue as any);
                }
              });
            } else {
              query = query.eq(key, value as any);
            }
          });
        }

        if (options.order) {
          options.order.forEach(([field, direction]) => {
            const snakeField = field.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
            query = query.order(snakeField, { ascending: direction === 'ASC' });
          });
        }

        if (options.limit) {
          query = query.limit(options.limit);
        }

        const { data, error } = await query;

        if (error) {
          console.error(`Error finding all ${tableName}:`, error);
          return [];
        }

        return data ? toCamelCase<T[]>(data) : [];
      } catch (err) {
        console.error(`Unexpected error finding all ${tableName}:`, err);
        return [];
      }
    },
    create: async (data: Partial<T>): Promise<T | null> => {
      try {
        if (!isSupabaseConfigured) {
          console.error(`Cannot create ${tableName}: Supabase is not configured`);
          return null;
        }

        const { data: result, error } = await supabase
          .from(tableName)
          .insert(toSnakeCase<Partial<TDB>>(data))
          .select()
          .single();

        if (error) {
          console.error(`Error creating ${tableName}:`, error);
          return null;
        }

        return toCamelCase<T>(result);
      } catch (err) {
        console.error(`Unexpected error creating ${tableName}:`, err);
        return null;
      }
    },
    update: async (data: Partial<T>, options: QueryOptions): Promise<T | null> => {
      try {
        if (!isSupabaseConfigured) {
          console.error(`Cannot update ${tableName}: Supabase is not configured`);
          return null;
        }

        let query = supabase.from(tableName).update(toSnakeCase<Partial<TDB>>(data));

        if (options.where) {
          Object.entries(toSnakeCase(options.where)).forEach(([key, value]) => {
            query = query.eq(key, value as any);
          });
        }

        const { data: result, error } = await query.select().single();

        if (error) {
          console.error(`Error updating ${tableName}:`, error);
          return null;
        }

        return toCamelCase<T>(result);
      } catch (err) {
        console.error(`Unexpected error updating ${tableName}:`, err);
        return null;
      }
    },
    destroy: async (options: QueryOptions): Promise<boolean> => {
      try {
        if (!isSupabaseConfigured) {
          console.error(`Cannot delete from ${tableName}: Supabase is not configured`);
          return false;
        }

        let query = supabase.from(tableName).delete();

        if (options.where) {
          Object.entries(toSnakeCase(options.where)).forEach(([key, value]) => {
            query = query.eq(key, value as any);
          });
        }

        const { error } = await query;

        if (error) {
          console.error(`Error deleting from ${tableName}:`, error);
          return false;
        }

        return true;
      } catch (err) {
        console.error(`Unexpected error deleting from ${tableName}:`, err);
        return false;
      }
    },
    count: async (options: QueryOptions = {}): Promise<number> => {
      try {
        if (!isSupabaseConfigured) {
          console.error(`Cannot count ${tableName}: Supabase is not configured`);
          return 0;
        }

        let query = supabase.from(tableName).select('*', { count: 'exact', head: true });

        if (options.where) {
          Object.entries(toSnakeCase(options.where)).forEach(([key, value]) => {
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
              // Handle operators
              Object.entries(value).forEach(([opKey, opValue]) => {
                if (opKey.includes('Op.')) {
                  const operator = opKey.replace('[Op.', '').replace(']', '');
                  switch (operator) {
                    case 'eq': query = query.eq(key, opValue as any); break;
                    case 'ne': query = query.neq(key, opValue as any); break;
                    case 'gt': query = query.gt(key, opValue as any); break;
                    case 'gte': query = query.gte(key, opValue as any); break;
                    case 'lt': query = query.lt(key, opValue as any); break;
                    case 'lte': query = query.lte(key, opValue as any); break;
                    case 'in': query = query.in(key, opValue as any[]); break;
                    case 'notIn': query = query.not(key, 'in', opValue as any[]); break;
                    case 'not': query = query.not(key, 'eq', opValue as any); break;
                    default: query = query.eq(key, opValue as any);
                  }
                } else {
                  query = query.eq(key, opValue as any);
                }
              });
            } else {
              query = query.eq(key, value as any);
            }
          });
        }

        const { count, error } = await query;

        if (error) {
          console.error(`Error counting ${tableName}:`, error);
          return 0;
        }

        return count || 0;
      } catch (err) {
        console.error(`Unexpected error counting ${tableName}:`, err);
        return 0;
      }
    }
  };
};

// Create model-like objects that mimic Sequelize models
const models = {
  Tenant: createModel<TenantType, TenantDBType>('tenants'),
  User: createModel<UserType, UserDBType>('users'),
  Service: createModel<ServiceType, ServiceDBType>('services'),
  StaffService: createModel<StaffServiceType, StaffServiceDBType>('staff_services'),
  Availability: createModel<AvailabilityType, AvailabilityDBType>('availabilities'),
  TimeOff: createModel<TimeOffType, TimeOffDBType>('time_offs'),
  Appointment: createModel<AppointmentType, AppointmentDBType>('appointments'),
  Notification: createModel<NotificationType, NotificationDBType>('notifications')
};

// Export the model interfaces
export type ModelTenant = typeof models.Tenant;
export type ModelUser = typeof models.User;
export type ModelService = typeof models.Service;
export type ModelStaffService = typeof models.StaffService;
export type ModelAvailability = typeof models.Availability;
export type ModelTimeOff = typeof models.TimeOff;
export type ModelAppointment = typeof models.Appointment;
export type ModelNotification = typeof models.Notification;

// Export the models
export const Tenant = models.Tenant;
export const User = models.User;
export const Service = models.Service;
export const StaffService = models.StaffService;
export const Availability = models.Availability;
export const TimeOff = models.TimeOff;
export const Appointment = models.Appointment;
export const Notification = models.Notification;

// Export utility functions
export { toCamelCase, toSnakeCase };
