import { Request, Response, NextFunction, RequestHandler } from 'express';

/**
 * Wraps a controller function to make it compatible with Express route handlers
 * This solves TypeScript errors related to return types
 */
export const asyncHandler = (
  fn: (req: Request, res: Response, next?: NextFunction) => Promise<any>
): RequestHandler => {
  return (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next))
      .then(() => {
        // Don't call next() - let the controller handle the response
      })
      .catch((error) => {
        next(error);
      });
  };
};

/**
 * Wraps middleware to ensure it returns void and properly calls next()
 */
export const wrapMiddleware = (
  middleware: (req: Request, res: Response, next: NextFunction) => Promise<any> | any
): RequestHandler => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const result = middleware(req, res, next);

      // If it's a promise, handle it
      if (result && typeof result.then === 'function') {
        result
          .catch((error: any) => {
            next(error);
          });
      }
    } catch (error) {
      next(error);
    }
  };
};
